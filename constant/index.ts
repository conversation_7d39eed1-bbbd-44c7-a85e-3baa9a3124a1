import { FATHER_TYPE_ENUM, IFatherInscription, PATH_ID_ENUM } from './type';
import ActionIcon from '../public/image/menus/action.svg';
import HairIcon from '../public/image/menus/hair.svg';
import PantsIcon from '../public/image/menus/pants.svg';
import ShirtIcon from '../public/image/menus/shirt.svg';
import ShoesIcon from '../public/image/menus/shoes.svg';
import PetIcon from '../public/image/menus/pet.svg';
import { SVG_FILE } from './staticFile';
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';

// 如果为true，表示使用本地资源，上线之前改为false
export const IS_LOCAL_TEST = process.env.USE_CONFIG === 'LOCAL_TEST_CONFIG';

//如果 AVATAR_VERSION参数未定义则是 浏览器版本
export const IS_AVATAR_PAGE = typeof process.env.AVATAR_VERSION === 'undefined';

// nft图浏览器接口地址 目前用在贴图
export const ORD_NFT_IMG_SERVER: string = process.env.ORD_NFT_IMG_SERVER as string;

// 当前是否为测试环境
export const IS_TEST_ENV =
  process.env.ENVIRONMENT !== 'online' &&
  process.env.ENVIRONMENT !== 'release' &&
  process.env.ENVIRONMENT !== 'contract';

// ord浏览器根域名
const ORD_SERVER = process.env.ORD_SERVER;
// ord浏览器根域名
const CDN_SERVER = process.env.CDN_SERVER || '';
const CDN_VERSION = process.env.CDN_VERSION || 'v0.0.0';

export const THEME_MEDIA_NUM = {
  ORD_BROWSER: IS_AVATAR_PAGE ? 600 : 0, //ordinals浏览器显示尺寸
  ORD_PREVIEW: IS_AVATAR_PAGE ? 301 : 0, //ordinals浏览器列表预览显示尺寸,unisat钱包显示尺寸
  FRONTEND_LARGE: IS_AVATAR_PAGE ? 1294 : 1, //官网大屏显示
};
export const THEME_MEDIA_ENUM = {
  ORD_BROWSER: `@media screen and (max-width: ${THEME_MEDIA_NUM.ORD_BROWSER}px)`, //ordinals浏览器显示尺寸
  ORD_PREVIEW: `@media screen and (max-width: ${THEME_MEDIA_NUM.ORD_PREVIEW}px)`, //ordinals浏览器列表预览显示尺寸,unisat钱包显示尺寸
  FRONTEND_LARGE: `@media screen and (min-width: ${THEME_MEDIA_NUM.FRONTEND_LARGE}px)`, //官网大屏显示
};

function loadAvatarModel(element: IFatherInscription, loaded: () => void) {
  let typeName = String(element.type);
  if (typeName === FATHER_TYPE_ENUM.Hat) {
    typeName = 'Heat';
  }

  let index = element.childrenInscription.length + 1;
  const iconPath =
    `./assets/${typeName}/icon_${typeName}_` + index.toString().padStart(2, '0') + `.png`;
  const glbPath = `./assets/${typeName}/${typeName}_` + index.toString().padStart(2, '0') + `.glb`;
  const loader = new GLTFLoader();

  loader.load(
    glbPath,
    (gltf) => {
      element.childrenInscription.push({
        inscriptionId: glbPath,
        metadata: {
          collection: 'uniworlds_' + typeName,
          version: 'v0.0.1',
          icon: iconPath,
        },
      });
      loadAvatarModel(element, loaded);
    },
    undefined,
    (error) => {
      console.error('no exist glb ', glbPath);
      loaded();
    }
  );
}

// 本地资源测试配置
const LOCAL_TEST_CONFIG = {
  // avatar铭文id
  AVATAR_INSCRIPTION_ID: {
    Body: './assets/MainBody.glb',
    Hat: './assets/Heat/Heat_00.glb',
    Pants: './assets/Pants/Pants_00.glb',
    Shirt: './assets/Shirt/Shirt_00.glb',
    Shoes: './assets/Shoes/Shoes_00.glb',
  },
  // 铭文配置
  DEFAULT_FATHER_INSCRIPTION: [
    {
      type: FATHER_TYPE_ENUM.Action, //类型名称
      pathIdKey: PATH_ID_ENUM.actionId, //对应IAvatarMetadata，用来对应渲染部件
      inscriptionId: ActionIcon.src,
      childrenInscription: [],
    },
    {
      type: FATHER_TYPE_ENUM.Pet, //类型名称
      pathIdKey: PATH_ID_ENUM.petId, //对应IAvatarMetadata，用来对应渲染部件
      inscriptionId: PetIcon.src,
      childrenInscription: [],
    },
    {
      type: FATHER_TYPE_ENUM.Pants,
      pathIdKey: PATH_ID_ENUM.pantsId,
      inscriptionId: PantsIcon.src,
      childrenInscription: [],
    },
    {
      type: FATHER_TYPE_ENUM.Hat,
      pathIdKey: PATH_ID_ENUM.hatId,
      inscriptionId: HairIcon.src,
      childrenInscription: [],
    },
    {
      type: FATHER_TYPE_ENUM.Shoes,
      pathIdKey: PATH_ID_ENUM.shoesId,
      inscriptionId: ShoesIcon.src,
      childrenInscription: [],
    },
    {
      type: FATHER_TYPE_ENUM.Shirt,
      pathIdKey: PATH_ID_ENUM.shirtId,
      inscriptionId: ShirtIcon.src,
      childrenInscription: [],
      // 支持设置贴图
      texture: {
        pathIdKey: PATH_ID_ENUM.shirtTextureId, //对应IAvatarMetadata的key，用来对应渲染部件
      },
      // 支持设置颜色
      color: {
        pathIdKey: PATH_ID_ENUM.shirtColor, //对应IAvatarMetadata的key，用来对应渲染部件
      },
    },
  ] as IFatherInscription[],
};
// 本地链测试链配置
const CHAIN_TEST_CONFIG = {
  AVATAR_INSCRIPTION_ID: {
    Body: '25208027eb4130ea6ed1b9a0ac770c77c8350de3114d19411a8f5da9a3dff5ebi0',
    Hat: '7fac2a60f8608ba932f04256989d609c7f53dab665a273f9fb102b547f37b8d3i0',
    Pants: 'e48963a104b4c7aaa70a3dc490e4a587ced5d552da63123b6ce94c58bc718570i0',
    Shirt: 'f1ced2bb3a720d27ff8702130ec2fe3c96ea54a1d6ce0ed889406a751ee7a92fi0',
    Shoes: '801d00c4a31f753e66209ca8c5c4237ea40f21d57a92989ff9b986c982dc317ei0',
  },
  DEFAULT_FATHER_INSCRIPTION: [
    {
      type: FATHER_TYPE_ENUM.Action, //类型名称
      pathIdKey: PATH_ID_ENUM.actionId, //对应IAvatarMetadata，用来对应渲染部件
      inscriptionId: '016485e67d88cf65fe90df9186388b9db6bf6291fea9215b7241fd817b74fc2di0', // 父铭文id
      childrenInscription: [], //子铭文 会去查父铭文下的所有子铭文
    },
    {
      type: FATHER_TYPE_ENUM.Pants,
      pathIdKey: PATH_ID_ENUM.pantsId,
      inscriptionId: '87a6d9ade40333993c3311e025121ff491b752ba39d38dcdf6ccde9f54545fcdi0',
      childrenInscription: [],
    },
    {
      type: FATHER_TYPE_ENUM.Hat,
      pathIdKey: PATH_ID_ENUM.hatId,
      inscriptionId: '62766e954248db0d71140550bb9cc28b64378492aba7336e32c67ae7c5020beai0',
      childrenInscription: [],
    },
    {
      type: FATHER_TYPE_ENUM.Shoes,
      pathIdKey: PATH_ID_ENUM.shoesId,
      inscriptionId: '2f98e7d9e0854cc7867d9c60a130fe732734fdd6087f53b7e795040d3274b7b9i0',
      childrenInscription: [],
    },
    {
      type: FATHER_TYPE_ENUM.Gloves,
      pathIdKey: PATH_ID_ENUM.glovesId,
      inscriptionId: 'f285673478bcc0665803cb79dabc574b852cc01e71463a8ae5e264278609ceb5i0',
      childrenInscription: [],
    },
    {
      type: FATHER_TYPE_ENUM.Shirt,
      pathIdKey: PATH_ID_ENUM.shirtId,
      inscriptionId: '82e4476dfc352cb67f468df7916be47b0fde51a5f1f56f31b8c51e54420af760i0',
      childrenInscription: [],
      // 支持设置贴图
      texture: {
        pathIdKey: PATH_ID_ENUM.shirtTextureId, //对应IAvatarMetadata的key，用来对应渲染部件
      },
      // 支持设置颜色
      color: {
        pathIdKey: PATH_ID_ENUM.shirtColor, //对应IAvatarMetadata的key，用来对应渲染部件
      },
    },
    {
      type: FATHER_TYPE_ENUM.Pet, //类型名称
      pathIdKey: PATH_ID_ENUM.petId, //对应IAvatarMetadata，用来对应渲染部件
      inscriptionId: '14b7567212e718889c619f6967d6d6b8c90699b7e68f906dca13da38900d7b87i0',
      childrenInscription: [],
    },
    // {
    //   type: FATHER_TYPE_ENUM.ShirtTexture,
    //   pathIdKey: PATH_ID_ENUM.shirtTextureId,
    //   inscriptionId: '92dc4f05a8e76fdf39fe35b4e3dbfce70c5757ed068184d6ce07a7b8ea6eaf49i0',
    //   childrenInscription: []
    // }
  ] as IFatherInscription[],
};
// 线上正式环境配置
const CHAIN_PRO_CONFIG = {
  TEST_USER_AVATAR_INSCRIPTION_ID: '', //置空
  AVATAR_INSCRIPTION_ID: {
    Body: '25208027eb4130ea6ed1b9a0ac770c77c8350de3114d19411a8f5da9a3dff5ebi0',
    Hat: '7fac2a60f8608ba932f04256989d609c7f53dab665a273f9fb102b547f37b8d3i0',
    Pants: 'e48963a104b4c7aaa70a3dc490e4a587ced5d552da63123b6ce94c58bc718570i0',
    Shirt: 'f1ced2bb3a720d27ff8702130ec2fe3c96ea54a1d6ce0ed889406a751ee7a92fi0',
    Shoes: '801d00c4a31f753e66209ca8c5c4237ea40f21d57a92989ff9b986c982dc317ei0',
  },
  DEFAULT_FATHER_INSCRIPTION: [
    {
      type: FATHER_TYPE_ENUM.Action, //类型名称
      pathIdKey: PATH_ID_ENUM.actionId, //对应IAvatarMetadata，用来对应渲染部件
      inscriptionId: '016485e67d88cf65fe90df9186388b9db6bf6291fea9215b7241fd817b74fc2di0', // 父铭文id
      childrenInscription: [], //子铭文 会去查父铭文下的所有子铭文
    },
    {
      type: FATHER_TYPE_ENUM.Pants,
      pathIdKey: PATH_ID_ENUM.pantsId,
      inscriptionId: '87a6d9ade40333993c3311e025121ff491b752ba39d38dcdf6ccde9f54545fcdi0',
      childrenInscription: [],
    },
    {
      type: FATHER_TYPE_ENUM.Hat,
      pathIdKey: PATH_ID_ENUM.hatId,
      inscriptionId: '62766e954248db0d71140550bb9cc28b64378492aba7336e32c67ae7c5020beai0',
      childrenInscription: [],
    },
    {
      type: FATHER_TYPE_ENUM.Shoes,
      pathIdKey: PATH_ID_ENUM.shoesId,
      inscriptionId: '2f98e7d9e0854cc7867d9c60a130fe732734fdd6087f53b7e795040d3274b7b9i0',
      childrenInscription: [],
    },
    {
      type: FATHER_TYPE_ENUM.Gloves,
      pathIdKey: PATH_ID_ENUM.glovesId,
      // TODO: 手套铭文上链之后更新下方铭文
      inscriptionId: 'f285673478bcc0665803cb79dabc574b852cc01e71463a8ae5e264278609ceb5i0',
      childrenInscription: [],
    },
    {
      type: FATHER_TYPE_ENUM.Shirt,
      pathIdKey: PATH_ID_ENUM.shirtId,
      inscriptionId: '82e4476dfc352cb67f468df7916be47b0fde51a5f1f56f31b8c51e54420af760i0',
      childrenInscription: [],
      // 支持设置贴图
      texture: {
        pathIdKey: PATH_ID_ENUM.shirtTextureId, //对应IAvatarMetadata的key，用来对应渲染部件
      },
      // 支持设置颜色
      color: {
        pathIdKey: PATH_ID_ENUM.shirtColor, //对应IAvatarMetadata的key，用来对应渲染部件
      },
    },
    {
      type: FATHER_TYPE_ENUM.Pet, //类型名称
      pathIdKey: PATH_ID_ENUM.petId, //对应IAvatarMetadata，用来对应渲染部件
      inscriptionId: '14b7567212e718889c619f6967d6d6b8c90699b7e68f906dca13da38900d7b87i0',
      childrenInscription: [],
    },
    // {
    //   type: FATHER_TYPE_ENUM.ShirtTexture,
    //   pathIdKey: PATH_ID_ENUM.shirtTextureId,
    //   inscriptionId: '92dc4f05a8e76fdf39fe35b4e3dbfce70c5757ed068184d6ce07a7b8ea6eaf49i0',
    //   childrenInscription: []
    // }
  ] as IFatherInscription[],
};

// 此配置为装饰副配置，例如 贴图 颜色，目的是为了排除渲染avatar时不把副配置当作主装饰使用----后续有新的副配置需要手动添加到这里，否则可能渲染报错
export const EXCLUDE_APPENDIX_PATH_ID = [PATH_ID_ENUM.shirtTextureId, PATH_ID_ENUM.shirtColor];

// 菜单分组
export const MENU_GROUP = [
  {
    title: 'Clothes Group',
    icon: SVG_FILE.shirtSelectIcon,
    unSelectIcon: SVG_FILE.shirtUnselectIcon,
    fathersType: [
      FATHER_TYPE_ENUM.Shirt,
      FATHER_TYPE_ENUM.Pants,
      FATHER_TYPE_ENUM.Shoes,
      FATHER_TYPE_ENUM.Hat,
    ], //匹配CONFIG中的type
  },
  {
    title: 'Hair Group',
    icon: SVG_FILE.kingSelectIcon,
    unSelectIcon: SVG_FILE.kingUnselectIcon,
    fathersType: [FATHER_TYPE_ENUM.Gloves],
  },
  {
    title: 'Pet Group',
    icon: SVG_FILE.petSelectIcon,
    unSelectIcon: SVG_FILE.petUnselectIcon,
    fathersType: [FATHER_TYPE_ENUM.Pet],
  },
  {
    title: 'Action Group',
    icon: SVG_FILE.actionSelectIcon,
    unSelectIcon: SVG_FILE.actionUnselectIcon,
    fathersType: [FATHER_TYPE_ENUM.Action],
  },
];

const CONFIG = (() => {
  switch (process.env.USE_CONFIG as string) {
    case 'CHAIN_PRO_CONFIG':
      return CHAIN_PRO_CONFIG;
    case 'CHAIN_TEST_CONFIG':
      return CHAIN_TEST_CONFIG;
    case 'LOCAL_TEST_CONFIG':
      return LOCAL_TEST_CONFIG;
  }
  throw new Error('USE_CONFIG value is not support');
})();

const { AVATAR_INSCRIPTION_ID, DEFAULT_FATHER_INSCRIPTION } = CONFIG;
export {
  ORD_SERVER,
  CDN_SERVER,
  CDN_VERSION,
  AVATAR_INSCRIPTION_ID,
  DEFAULT_FATHER_INSCRIPTION,
  loadAvatarModel,
};
