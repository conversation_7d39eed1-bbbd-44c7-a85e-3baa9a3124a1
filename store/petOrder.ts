import { createSlice, PayloadAction } from '@reduxjs/toolkit';

export interface IPetOrder {
  txidArr: string[];
}

const initialState: IPetOrder = {
  txidArr: [],
};

export const petOrderSlice = createSlice({
  name: 'petOrder',
  initialState,
  reducers: {
    updatePetOrder(state, action: PayloadAction<IPetOrder>) {
      state.txidArr = action.payload.txidArr ?? state.txidArr;
    },
  },
});
export const { updatePetOrder } = petOrderSlice.actions;
export default petOrderSlice.reducer;
