import Modal from '../../BasicComponents/Modal';
import { useEffect, useMemo, useState } from 'react';
import Slider from 'rc-slider';
import 'rc-slider/assets/index.css';
import {
  getFeeRate,
  IFeeRateRespData,
  petWeb3Broadcast,
  petWeb3Generator,
  petWeb3Merge,
} from '@/server';
import BigNumber from 'bignumber.js';
import { getPublicKey, signPsbt } from '@/utils';
import toast from 'react-hot-toast';
import { useAppDispatch, useAppSelector } from '@/hooks/useStore';
import { updateModalState } from '@/store/modal';
import { updatePetOrder } from '@/store/petOrder';
import styled, { css } from 'styled-components';
import {
  CloseIcon,
  StyledDialogBody,
  StyledTitle,
  StyledFooter,
} from '@/components/Basic/ModalContentElement';
import BasicButton from '@/components/Basic/Button';
import { KeyPressUtil } from '@/game/Global/GlobalKeyPressUtil';

interface IPetSubmitChainProps {
  onClose: () => void;
}

enum EFF_ENUM {
  NORMAL = 'Normal',
  FAST = 'Fast',
  CUSTOM = 'Custom',
}

function satsToDollar(sats: number | BigNumber, dollarPrice: number) {
  return BigNumber(sats)
    .div(10 ** 8)
    .times(dollarPrice)
    .dp(6);
}

export default function SubmitModal({ onClose }: IPetSubmitChainProps) {
  const { btcAddress, btcWallet } = useAppSelector((state) => state.AppReducer);
  const petSubmitChainModalConfig = useAppSelector(
    (state) => state.ModalReducer.petSubmitChainModalConfig
  );
  const txidArr = useAppSelector((state) => state.PetOrderReducer.txidArr);
  const dispatch = useAppDispatch();
  const [fee, setFee] = useState<number>(0);
  const [customFee, setCustomFee] = useState(0);
  const [feeType, setFeeType] = useState<EFF_ENUM>(EFF_ENUM.FAST);

  const currentFee = useMemo(() => {
    if (feeType === EFF_ENUM.CUSTOM) return customFee;
    return fee;
  }, [fee, customFee, feeType]);

  const [submitLoading, setSubmitLoading] = useState<boolean>(false);

  const [recommendedFees, setRecommendedFees] = useState<IFeeRateRespData>({
    feesRecommended: {
      fastestFee: 0,
      halfHourFee: 0,
      hourFee: 0,
      economyFee: 0,
      minimumFee: 0,
      updateTime: 0,
    },
    generatorSats: {
      inscription: 0,
      base: 0,
    },
    price: 0,
  });

  const handleClose = () => {
    setFee(0);
    setCustomFee(0);
    setFeeType(EFF_ENUM.FAST);
    setSubmitLoading(false);
    dispatch(
      updateModalState({
        petSubmitChainModalConfig: {
          isOpen: false,
        },
      })
    );
  };

  useEffect(() => {
    if (petSubmitChainModalConfig.isOpen && btcAddress) {
      try {
        getFeeRate().then((res) => {
          const data = res.data.data;
          if (res.data.code === 1) {
            setRecommendedFees(data);
            setFee(data.feesRecommended.fastestFee);
            setCustomFee(data.feesRecommended.fastestFee);
          } else {
            toast.error(res.data.msg);
          }
        });
      } catch (error) {}
    }
  }, [btcAddress, petSubmitChainModalConfig]);

  const handleUpdateOrder = (txid: string) => {
    const newArr = [...txidArr];
    newArr.push(txid);
    dispatch(updatePetOrder({ txidArr: newArr }));
  };

  const onConfirm = async () => {
    if (!btcAddress || !currentFee) {
      return;
    }
    setSubmitLoading(true);
    try {
      const pubKey = await getPublicKey(btcWallet);
      const data = { address: btcAddress, pubKey, feeRate: currentFee };
      const mergeIds = petSubmitChainModalConfig.configData?.mergeData?.ids;
      const isMerge = mergeIds && mergeIds.length > 0;
      const petData = isMerge
        ? await petWeb3Merge({ ...data, alkaneIds: mergeIds })
        : await petWeb3Generator(data);
      if (petData.data.code === 1) {
        const petRespData = petData.data.data;
        const { orderId, unsignedPsbt } = petRespData;
        const signedPsbtHex = await signPsbt(btcWallet, unsignedPsbt, btcAddress);
        if (signedPsbtHex) {
          const broadcastData = isMerge
            ? {
                signedPsbt: signedPsbtHex,
                orderId,
                slotRecordId: petSubmitChainModalConfig.configData?.mergeData?.slotRecordId,
              }
            : {
                signedPsbt: signedPsbtHex,
                orderId,
                userItemId: petSubmitChainModalConfig.configData?.generateData?.userItemId,
                positionTag: petSubmitChainModalConfig.configData?.generateData?.positionTag,
              };

          const broadcastRes = await petWeb3Broadcast(broadcastData);
          if (broadcastRes.data.code === 1) {
            const broadcastResData = broadcastRes.data.data;
            toast.success('Success!', {
              duration: 4000,
            });
            if (broadcastResData.txid) {
              handleUpdateOrder(broadcastResData.txid);
            }
            petSubmitChainModalConfig.confirmCallback?.();
            handleClose();
            setSubmitLoading(false);
          } else {
            toast.error(petData.data.msg);
            setSubmitLoading(false);
          }
        } else {
          setSubmitLoading(false);
        }
      } else {
        toast.error(petData.data.msg);
        setSubmitLoading(false);

        // const
      }
    } catch (error) {
      console.error(error);
      setSubmitLoading(false);
    }
  };

  const satsInItem = satsToDollar(
    recommendedFees.generatorSats.inscription,
    recommendedFees.price
  ).toFormat();

  const netWorkFee = recommendedFees.generatorSats.base * currentFee;
  const netWorkFeeDollar = satsToDollar(netWorkFee, recommendedFees.price).toFormat();
  const totalSats = BigNumber(recommendedFees.generatorSats.inscription).plus(
    BigNumber(netWorkFee)
  );
  const totalSatsDollar = satsToDollar(totalSats, recommendedFees.price).toFormat();

  useEffect(() => {
    if (petSubmitChainModalConfig.isOpen) {
      KeyPressUtil.setEnable(false);
    } else {
      KeyPressUtil.setEnable(true);
    }
  }, [petSubmitChainModalConfig]);

  if (!petSubmitChainModalConfig.isOpen) return null;

  return (
    <Modal visible={petSubmitChainModalConfig.isOpen} emptyOnly onClose={handleClose}>
      <StyledPetSubmitModalView>
        <CloseIcon onCloseClick={handleClose} />
        <StyledTitle>
          <span>Confirmation</span>
        </StyledTitle>
        <ModalBodyWrapper>
          <Fees
            fee={fee}
            setFee={setFee}
            feeType={feeType}
            setFeeType={setFeeType}
            customFee={customFee}
            setCustomFee={setCustomFee}
            recommendedFees={recommendedFees.feesRecommended}
          />
          <StyledTotalView className="total-view">
            <StyledValueItem className="top-value">
              <span>Sats In Item:</span>
              <div>
                <strong>
                  <span>
                    {BigNumber(recommendedFees.generatorSats.inscription).toFormat()} sats
                  </span>
                </strong>
                <span> ≈ ${satsInItem}</span>
              </div>
            </StyledValueItem>
            <StyledValueItem className="top-value">
              <span>Network Fee:</span>
              <div>
                <strong>
                  <span>≈{BigNumber(netWorkFee).toFormat()} sats</span>
                </strong>
                <span> ≈ ${netWorkFeeDollar}</span>
              </div>
            </StyledValueItem>
            <StyledValueItem className="top-value">
              <span>Total:</span>
              <div>
                <strong>
                  <span>≈{totalSats.toFormat()} sats</span>
                </strong>
                <span>≈ ${totalSatsDollar}</span>
              </div>
            </StyledValueItem>
          </StyledTotalView>
        </ModalBodyWrapper>

        <StyledFooter>
          <BasicButton $type="cancel" onClick={handleClose}>
            <span>Cancel</span>
          </BasicButton>
          <BasicButton
            onClick={onConfirm}
            disabled={submitLoading || !btcAddress || currentFee <= 0}>
            <span>Confirm</span>
          </BasicButton>
        </StyledFooter>
      </StyledPetSubmitModalView>
    </Modal>
  );
}

const ModalBodyWrapper = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  gap: 1rem;
  width: 100%;
`;

const StyledValueItem = styled.div`
  display: flex;
  height: 1.375rem;
  justify-content: space-between;
  align-items: center;
  align-self: stretch;
  & > span {
    color: #140f08;
    font-family: Inter;
    font-size: 1rem;
    font-style: normal;
    font-weight: 700;
    line-height: normal;
    text-transform: capitalize;
  }
  & > div {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    & > strong > span {
      color: #a58061;
      font-size: 0.875rem;
    }
    & > span {
      color: #140f08;
      font-size: 1.125rem;
      font-weight: 400;
    }
  }
`;

const StyledTotalView = styled.div`
  display: flex;
  padding: 1rem;
  flex-direction: column;
  align-items: flex-start;
  gap: 1rem;
  align-self: stretch;
  border-radius: 1.5rem;
  border: 0.0625rem solid #a58061;
`;

const StyledPetSubmitModalView = styled(StyledDialogBody)`
  width: 38rem;
  * {
    box-sizing: border-box;
  }
  ${StyledFooter} {
    width: 100%;
    & > button {
      flex: 1;
    }
  }
`;

function Fees({
  fee,
  setFee,
  customFee,
  setCustomFee,
  feeType,
  setFeeType,
  recommendedFees,
}: {
  fee: number;
  setFee: (fee: number) => void;
  customFee: number;
  setCustomFee: (fee: number) => void;
  feeType: EFF_ENUM;
  setFeeType: (fee: EFF_ENUM) => void;
  recommendedFees: IFeeRateRespData['feesRecommended'];
}) {
  return (
    <StyledFeeContainer>
      <StyledItemWrapper>
        <StyleFeeItem
          $active={feeType === EFF_ENUM.NORMAL}
          onClick={() => {
            setFeeType(EFF_ENUM.NORMAL);
            setFee(recommendedFees.economyFee);
          }}>
          <h2>Economy</h2>
          <p>
            <span>{recommendedFees.economyFee}</span>
            <i>sats/vB</i>
          </p>
        </StyleFeeItem>
        <StyleFeeItem
          $active={feeType === EFF_ENUM.FAST}
          onClick={() => {
            setFeeType(EFF_ENUM.FAST);
            setFee(recommendedFees.halfHourFee);
          }}>
          <h2>Normal</h2>
          <p>
            <span>{recommendedFees.halfHourFee}</span>
            <i>sats/vB</i>
          </p>
        </StyleFeeItem>
        <StyleFeeItem
          $active={feeType === EFF_ENUM.CUSTOM}
          onClick={() => setFeeType(EFF_ENUM.CUSTOM)}>
          <h2>Custom</h2>
          <p>
            <span>{customFee}</span>
            <i>sats/vB</i>
          </p>
        </StyleFeeItem>
      </StyledItemWrapper>
      {feeType === EFF_ENUM.CUSTOM && (
        <StyledCustomFeeContainer>
          <div className="custom-slider">
            <Slider
              value={customFee}
              min={1}
              max={1000}
              onChange={(value) => setCustomFee(value as number)}
            />
          </div>
          <input
            type="number"
            min={1}
            max={1000}
            value={customFee}
            onChange={(e) => {
              const val = e.target.value;
              if (val && !isNaN(+val)) {
                setCustomFee(Math.min(Math.abs(+val || 1), 1000));
              }
            }}
          />
        </StyledCustomFeeContainer>
      )}
    </StyledFeeContainer>
  );
}
const StyleFeeItem = styled.div<{ $active: boolean }>`
  display: flex;
  height: 4.5rem;
  padding: 0 1.5rem;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 0.5rem;
  flex: 1 0 0;
  border-radius: 1rem;
  background: #fff;
  cursor: pointer;
  transition:
    background 0.3s ease-in,
    border 0.3s ease-in;
  font-family: 'JetBrains Mono';
  & > h2 {
    margin: 0;
    font-size: 1rem;
    font-weight: 700;
    color: #140f08;
    text-transform: capitalize;
  }
  & > p {
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;

    & > span {
      font-weight: 700;
      color: #140f08;
      font-size: 1.125rem;
    }
    & > i {
      font-size: 0.875rem;
      font-weight: 400;
      font-style: normal;
      color: #a58061;
    }
  }

  ${({ $active }) =>
    $active
      ? css`
          border-radius: 1rem;
          border: 0.0625rem solid #ff8316;
          background: #ffdfc3;
          & > h2,
          & > p > span,
          & > p > i {
            color: #f27100;
          }
        `
      : css`
          border: 0.0625rem solid #fff;
        `}
`;

const StyledItemWrapper = styled.div`
  display: flex;
  align-items: center;
  gap: 1rem;
  align-self: stretch;
`;
const StyledCustomFeeContainer = styled.div`
  display: flex;
  align-items: center;
  gap: 1rem;
  align-self: stretch;
  .custom-slider {
    flex: 1;
    .rc-slider-rail {
      background: #ff831633;
    }
    .rc-slider-handle {
      border: 0;
      width: 0.75rem;
      height: 0.75rem;
      margin-top: -0.3125rem;
      background-color: #ff8316;
    }
    .rc-slider-step,
    .rc-slider-rail {
      height: 0.125rem;
    }
    .rc-slider-track {
      background: #ff831680;
      height: 0.125rem;
    }
    .rc-slider-handle-dragging.rc-slider-handle-dragging.rc-slider-handle-dragging {
      box-shadow: none;
    }
  }
  & > input {
    width: 5rem;
    height: 2.5rem;
    background: #fff;
    border: 0.0625rem solid #ff8316;
    box-sizing: border-box;
    border-radius: 0.75rem;
    outline: none;
    padding: 0 0.625rem;
  }
`;

const StyledFeeContainer = styled.div`
  display: flex;
  padding: 1rem;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  gap: 1rem;
  align-self: stretch;
  border-radius: 1.5rem;
  border: 0.0625rem solid #a58061;
`;
