import { useAppSelector } from '@/hooks/useStore';
import { useMemo, useRef, useState } from 'react';
import { IBagPetList, SCENE_TYPE } from '@/constant/type';
import { PetStatus } from '@/constant/enum';
import Image from 'next/image';
import { AnimatePresence } from 'framer-motion';
import SvgWrapper, { SpriteSvg } from '@/components/SvgWrapper';
import { useContextDispatch, useContextSelector } from '../../context';
import {
  PetStatusItem,
  PetStatusList,
  PetStatusImageBox,
  StyledCursor,
  PetStamina,
  PetImageStaminBox,
  CursorBtnContainer,
  PetOperationList,
  PetOperationItem,
  StyledOperationSvg,
  PetOperationListWrapper,
} from './style';
import { changePetStatus } from '@/server';
import toast from 'react-hot-toast';
import useClickAway from '@/hooks/useClickAway';
import useUpdatePetListItem from '@/hooks/useUpdatePetListItem';
import { basicOperationArr, featureOperationConfigArr } from '../../constant';
import { StatusTag } from '../PetDetailPanel/style';
import styled from 'styled-components';

const colorArr = [
  {
    color: '#2FF4A5',
    limit: 80,
  },
  {
    color: '#DDF42F',
    limit: 50,
  },
  {
    color: '#FF8316',
    limit: 20,
  },
  {
    color: '#FF2720',
    limit: 0,
  },
];

const OperationList = ({ petItem }: { petItem: IBagPetList }) => {
  const { updateGameStatePetItemData } = useUpdatePetListItem();
  const contextDispatch = useContextDispatch();
  const isChangePetStatusLoading = useContextSelector((state) => state.isChangePetStatusLoading);
  const { sceneType } = useAppSelector((state) => state.AppReducer);

  const handleChangePetStatus = async (petStatus: PetStatus) => {
    if (isChangePetStatusLoading) return;
    contextDispatch({
      type: 'UPDATE',
      payload: {
        isChangePetStatusLoading: true,
      },
    });
    try {
      const res = await changePetStatus({ petId: petItem._id, petStatus });
      if (res.data.code === 1) {
        const newPetData = res.data.data;
        updateGameStatePetItemData(newPetData);
      } else {
        toast.error(res.data.msg);
      }
      contextDispatch({
        type: 'UPDATE',
        payload: {
          isChangePetStatusLoading: false,
        },
      });
    } catch (error) {
      contextDispatch({
        type: 'UPDATE',
        payload: {
          isChangePetStatusLoading: false,
        },
      });
    }
  };
  const isInIsland = sceneType === SCENE_TYPE.Island;

  const operationConfigArr = useMemo(() => {
    const featureList = petItem.featureInfos.map((item) => item.feature);
    const list = isInIsland
      ? featureOperationConfigArr.filter((item) => featureList.includes(item.action))
      : [];
    return basicOperationArr.toSpliced(1, 0, ...list);
  }, [isInIsland, petItem]);

  return (
    <>
      {operationConfigArr.map((item) => {
        const disabled = !isInIsland && ![PetStatus.IDLE, PetStatus.FOLLOW].includes(item.action);

        return (
          <PetOperationItem
            key={item.name}
            $active={petItem.petStatus === item.action}
            $loading={isChangePetStatusLoading}
            $disabled={disabled}
            onClick={(e) => {
              e.preventDefault();
              if (disabled) return;
              if (petItem.petStatus !== item.action) {
                handleChangePetStatus(item.action);
              }
            }}>
            <StyledOperationSvg>
              <SpriteSvg id={item.iconId} />
            </StyledOperationSvg>
            <span>{item.name}</span>
          </PetOperationItem>
        );
      })}
    </>
  );
};

const StyledStatusSvg = styled(SvgWrapper)`
  height: 1.25rem;
  flex-shrink: 0;
`;

const StyledStatusTag = styled(StatusTag)`
  min-width: 4.625rem;
  height: 1.25rem;
  & > span:last-of-type {
    padding: 0;
    & > span {
      font-size: 0.6875rem;
    }
  }
`;

const PetStatusPanel = () => {
  const petList = useAppSelector((state) => state.GameReducer.petList);
  const currentSummonList = useMemo(() => {
    return petList
      .filter((item) => item.petStatus !== PetStatus.REST)
      .sort((a, b) => (a.lastSummonedAt < b.lastSummonedAt ? -1 : 1));
  }, [petList]);

  const contextDispatch = useContextDispatch();
  const selectedPetId = useContextSelector((state) => state.selectedPetId);
  const isExpand = useContextSelector((state) => state.isExpand);

  const handleClick = (id: string) => {
    if (isExpand && id === selectedPetId) {
      contextDispatch({
        type: 'UPDATE',
        payload: {
          selectedPetId: '',
          isExpand: false,
        },
      });
    } else {
      contextDispatch({
        type: 'UPDATE',
        payload: {
          selectedPetId: id,
          isExpand: true,
        },
      });
    }
  };

  const statusIconArr = [...basicOperationArr, ...featureOperationConfigArr];

  return (
    <PetStatusList>
      {currentSummonList.map((item, idx) => {
        const active = selectedPetId === item._id;
        const percent = (item.currentStamina / item.stamina) * 100;
        let color = colorArr[0].color;
        for (let index = 0; index < colorArr.length; index++) {
          const item = colorArr[index];
          if (percent >= item.limit) {
            color = item.color;
            break;
          }
        }

        const currentOperation =
          statusIconArr.find((it) => it.action === item.petStatus) || statusIconArr[0];

        return (
          <PetStatusItem key={'petStatus' + item._id}>
            <CursorBtn petItem={item} />

            <PetImageStaminBox
              onClick={(e) => {
                e.preventDefault();
                handleClick(item._id);
              }}>
              <PetStatusImageBox $active={active}>
                <Image
                  width={512}
                  height={512}
                  src={item.bagConfigInfo.infoImageUrl}
                  loading="lazy"
                  alt="pet image"
                  draggable={false}
                />
                <StyledStatusTag
                  $borderColor={currentOperation.panelColorConfig.borderColor}
                  $color={currentOperation.panelColorConfig.color}>
                  <StyledStatusSvg>
                    <SpriteSvg id="petStatus" />
                  </StyledStatusSvg>
                  <span>
                    <span data-text={currentOperation.panelDisplay}>
                      {currentOperation.panelDisplay}
                    </span>
                  </span>
                </StyledStatusTag>
              </PetStatusImageBox>
              <PetStamina $percent={isNaN(percent) ? 0 : percent} $bgColor={color} />
            </PetImageStaminBox>
          </PetStatusItem>
        );
      })}
    </PetStatusList>
  );
};

export default PetStatusPanel;

const CursorBtn = ({ petItem }: { petItem: IBagPetList }) => {
  const selectedPetId = useContextSelector((state) => state.selectedPetId);
  const contextDispatch = useContextDispatch();
  const active = selectedPetId === petItem._id;
  const [showList, setShowList] = useState(false);

  const cursorRef = useRef<HTMLSpanElement>(null);
  const listRef = useRef<HTMLDivElement>(null);

  const handleCursorClick = (id: string) => {
    contextDispatch({
      type: 'UPDATE',
      payload: {
        selectedPetId: id,
      },
    });
    setShowList(true);
  };

  useClickAway(() => {
    setShowList(false);
  }, [cursorRef, listRef]);

  return (
    <CursorBtnContainer>
      <StyledCursor
        ref={cursorRef}
        $active={active}
        onClick={(e) => {
          e.preventDefault();
          handleCursorClick(petItem._id);
        }}>
        <SpriteSvg id="cursor" />
      </StyledCursor>
      <PetOperationListWrapper ref={listRef}>
        <AnimatePresence>
          {active && showList && (
            <PetOperationList
              ref={listRef}
              key={petItem._id + 'operationListAnimate'}
              initial={{
                opacity: 0,
              }}
              animate={{
                opacity: 1,
              }}
              exit={{
                opacity: 0,
              }}
              transition={{
                duration: 0.3,
                delay: 0.1,
                ease: 'easeInOut',
              }}>
              <OperationList petItem={petItem} />
            </PetOperationList>
          )}
        </AnimatePresence>
      </PetOperationListWrapper>
    </CursorBtnContainer>
  );
};
