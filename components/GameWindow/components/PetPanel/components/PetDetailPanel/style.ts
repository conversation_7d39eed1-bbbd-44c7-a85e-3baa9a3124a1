import styled from 'styled-components';

export const PetInfoSection = styled.div`
  display: flex;
  width: 25.5rem;
  padding: 0.5rem;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  gap: 1rem;
  border-radius: 1.5rem;
  background: #f7e7cd;
  backdrop-filter: blur(0.25rem);
`;

export const PetInfoSectionTitle = styled.p`
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
  align-self: stretch;
  margin: 0;
  span {
    color: #140f08;
    text-align: center;
    font-family: 'JetBrains Mono';
    font-size: 0.75rem;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
    letter-spacing: -0.03rem;
    text-transform: capitalize;
  }
  &::after,
  &::before {
    display: block;
    content: '';
    width: 0.375rem;
    height: 0.375rem;
    transform: rotate(45deg);
    flex-shrink: 0;
    border-radius: 0.0625rem;
    background: #ff8316;
  }
`;

export const PetInfoSectionInfoBox = styled.div`
  display: flex;
  justify-content: flex-start;
  align-items: center;
  gap: 0.375rem 1rem;
  flex-wrap: wrap;
  width: 100%;
`;

export const PetInfoItem = styled.span`
  display: flex;
  height: 1.75rem;
  padding: 0 0.625rem;
  align-items: center;
  gap: 0.25rem;
  border-radius: 0.5rem;
  background: #fbf4e8;
  font-family: Inter;
  font-size: 0.875rem;
  font-style: normal;
  line-height: 100%;
  width: calc(50% - 0.5rem);
  flex-shrink: 0;
  flex-grow: 0;
`;

export const PetInfoItemLabel = styled.span`
  color: #a58061;
  font-weight: 400;
`;
export const PetInfoItemValue = styled.span`
  color: #140f08;
  font-weight: 700;
`;
export const PetInfoItemValueRate = styled.span`
  font-weight: 700;
  margin-left: auto;
  & > span:first-of-type {
    color: #140f08;
  }
  & > span:last-of-type {
    color: #a58061;
  }
`;

export const BtnWrapper = styled.div`
  width: 100%;
  height: 100%;
  display: flex;
  margin-top: auto;
  align-items: center;
  justify-content: flex-end;
  gap: 1rem;
`;

export const PetInfoSectionMainBox = styled.div`
  display: flex;
  width: 25.5rem;
  height: 11.75rem;
  padding: 0.5rem 1rem;
  align-items: flex-start;
  flex-wrap: nowrap;
  gap: 0.5rem;
  border-radius: 1.5rem;
  background: #f7e7cd;
  backdrop-filter: blur(0.25rem);
`;

export const ImageBox = styled.div<{ $bgSrc: string }>`
  width: 10rem;
  height: 100%;
  position: relative;
  background: url(${({ $bgSrc }) => $bgSrc});
  background-size: contain;
  background-position: center bottom;
  background-repeat: no-repeat;
  position: relative;
`;

export const StatusTag = styled.span<{ $borderColor: string; $color: string }>`
  min-width: 6.5rem;
  height: 1.75rem;
  flex-shrink: 0;
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);

  & > span:first-of-type {
    width: 100%;
    color: ${({ $borderColor }) => $borderColor};
    filter: drop-shadow(0.16175rem 0.16175rem 0.6470625rem rgba(0, 0, 0, 0.4));
    position: absolute;
    top: 0;
    left: 0;
    z-index: -1;
  }
  & > span:last-of-type {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    padding: 0 1rem;
    & > span {
      position: relative;
      color: ${({ $color }) => $color};
      font-family: Inter;
      font-size: 1rem;
      font-style: italic;
      font-weight: 900;
      line-height: 100%;
      z-index: 1;
      &::before {
        display: flex;
        align-items: center;
        color: ${({ $borderColor }) => $borderColor};
        justify-content: center;
        content: attr(data-text);
        position: absolute;
        -webkit-text-stroke: 0.15rem currentColor;
        z-index: -1;
        left: 0;
      }
    }
  }
`;

export const RarityTag = styled.span<{ $color: string }>`
  display: inline-flex;
  padding: 0.25rem 0.375rem;
  justify-content: center;
  align-items: center;
  gap: 0.625rem;
  border-radius: 0.5rem;
  color: #fff;
  background: ${({ $color }) => $color};
  position: absolute;
  top: 0;
  left: 0;
  text-transform: capitalize;
`;

export const RightInfoBox = styled.div`
  flex: 1;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
  gap: 1rem;
`;

export const PetPropertyNameWrapper = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  width: 100%;
`;

export const PetNameBox = styled.div`
  display: flex;
  padding: 0.25rem 0.375rem 0.25rem 0.75rem;
  gap: 0.625rem;
  flex: 1;
  border-radius: 0.5rem;
  background: #fbf4e8;
  flex-wrap: nowrap;
  height: 2rem;

  & > span:first-of-type {
    color: #140f08;
    font-family: 'JetBrains Mono';
    font-size: 1rem;
    font-style: normal;
    font-weight: 500;
    letter-spacing: -0.04rem;
    user-select: text;
    flex: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 9.625rem;
    text-align: left;
  }
`;

export const SpMpContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 1rem;
  align-self: stretch;
  & > ${PetInfoItem} {
    width: 100%;
  }
`;
