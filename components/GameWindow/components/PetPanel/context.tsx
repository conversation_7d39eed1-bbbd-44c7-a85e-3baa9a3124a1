import { createTemplateContext } from '@/utils/createTemplateContext';

interface PetPanelContext {
  isExpand: boolean;
  selectedPetId: string;
  onDetailPanelClose: () => void;
  isChangePetStatusLoading: boolean;
  isRenameModalOpen: boolean;
  isReleaseModalOpen: boolean;
}

const initState: PetPanelContext = {
  isExpand: false,
  selectedPetId: '',
  onDetailPanelClose: () => false,
  isChangePetStatusLoading: false,
  isRenameModalOpen: false,
  isReleaseModalOpen: false,
};

const {
  TemplateContextProvider: PetPanelContextProvider,
  useContextDispatch,
  useContextSelector,
} = createTemplateContext<PetPanelContext>(initState);
export { PetPanelContextProvider, useContextDispatch, useContextSelector };
