import Dialog from '@/commons/Dialog';
import { useAppDispatch, useAppSelector } from '@/hooks/useStore';
import { PetBedManufactureTitle, PetBedManufactureTitleContainer } from '../SynthesisSystem/styles';
import ModalContent from '@/commons/ModalContent';
import styled, { css } from 'styled-components';
import { updateModalState } from '@/store/modal';
import PetFusionRecipe from './components/PetFusionRecipe';
import { PetFusionContextProvider, useContextDispatch, useContextSelector } from './context';
import PetFusionContent from './components/PetFusionContent';
import { useEffect, useRef } from 'react';
import { petFusion } from '@/server';
import toast from 'react-hot-toast';
import { PetShedRecordStatus, CHAIN_TYPE_ENUM } from '@/constant/enum';
import { updateGameState } from '@/store/game';
import useBagInventory from '@/hooks/useBagInventory';
import useFetchPetShedInfo from '@/hooks/useFetchPetShedInfo';

const StyledModalContent = styled(ModalContent)`
  border: 0.5rem solid #efbd73;
  box-shadow: none;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 3.5rem 2rem;
  gap: 2rem;
  border-radius: 3rem;
  * {
    box-sizing: border-box;
  }
  & > div:nth-of-type(2) {
    width: 100%;
    height: 100%;
    overflow: hidden;
  }
`;

const PetFusionContainer = () => {
  const dispatch = useAppDispatch();
  const contextDispatch = useContextDispatch();
  const petFusionModalConfig = useAppSelector((state) => state.ModalReducer.petFusionModalConfig);
  const selectedPetSlot = useContextSelector((state) => state.selectedPetSlot);
  const fusionLoading = useContextSelector((state) => state.fusionLoading);
  const petList = useAppSelector((state) => state.GameReducer.petList);
  const petShedInfo = useAppSelector((state) => state.GameReducer.petShedInfo);
  const callbackRef = useRef<() => void>(() => false);
  const { getPetListData } = useBagInventory();
  const { fetchPetShedInfo, updateReduxPetShed } = useFetchPetShedInfo();

  const resetContext = () => {
    contextDispatch({
      type: 'UPDATE',
      payload: {
        selectedPetSlot: {},
        clickPetItemId: '',
      },
    });
  };
  const handleClose = () => {
    dispatch(
      updateModalState({
        petFusionModalConfig: {
          isOpen: false,
          configData: {
            slotRecordId: '',
          },
        },
      })
    );
    resetContext();
  };

  callbackRef.current = handleClose;

  useEffect(() => {
    resetContext();
  }, [petFusionModalConfig]);

  useEffect(() => {
    return () => {
      resetContext();
    };
  }, []);

  const canSubmit = selectedPetSlot.first && selectedPetSlot.second;
  const isBindSelected = !!(
    selectedPetSlot.first?.bindingFlag || selectedPetSlot.second?.bindingFlag
  );

  const handlePetFusion = async () => {
    if (fusionLoading) return;

    if (selectedPetSlot.first?._id && selectedPetSlot.second?._id) {
      const data = {
        firstPetId: selectedPetSlot.first._id,
        secondPetId: selectedPetSlot.second._id,
        slotRecordId: petFusionModalConfig.configData.slotRecordId,
      };
      try {
        contextDispatch({ type: 'UPDATE', payload: { fusionLoading: true } });

        const res = await petFusion(data);
        if (res.data.code === 1) {
          contextDispatch({ type: 'UPDATE', payload: { fusionLoading: false } });
          const respData = res.data.data;
          const oldIdList = [data.firstPetId, data.secondPetId];

          const petShedIdx = petShedInfo.findIndex(
            (item) => item.slotRecordId === data.slotRecordId
          );
          if (petShedIdx === -1) {
            fetchPetShedInfo();
          } else {
            updateReduxPetShed({
              waitingEndTime: respData.waitingEndTime,
              slotRecordId: respData.slotRecordId,
              status: PetShedRecordStatus.GENERATING,
            });
          }

          const newList = petList.filter((item) => !oldIdList.includes(item._id));
          dispatch(updateGameState({ petList: newList }));
          getPetListData();
          handleClose();
        } else {
          contextDispatch({ type: 'UPDATE', payload: { fusionLoading: false } });

          toast.error(res.data.msg);
        }
      } catch (error) {
        console.log(error);
        contextDispatch({ type: 'UPDATE', payload: { fusionLoading: false } });
      }
    }
  };

  const handleChainPetFusion = () => {
    if (selectedPetSlot.first?.alkaneId && selectedPetSlot.second?.alkaneId) {
      const ids = [selectedPetSlot.first.alkaneId, selectedPetSlot.second.alkaneId];
      const oldIdList = [selectedPetSlot.first._id, selectedPetSlot.second._id];
      dispatch(
        updateModalState({
          petSubmitChainModalConfig: {
            isOpen: true,
            configData: {
              mergeData: {
                ids,
                slotRecordId: petFusionModalConfig.configData.slotRecordId,
              },
            },
            confirmCallback: () => {
              callbackRef.current?.();
              const newList = petList.filter((item) => !oldIdList.includes(item._id));
              dispatch(updateGameState({ petList: newList }));
              updateReduxPetShed({
                slotRecordId: petFusionModalConfig.configData.slotRecordId,
                status: PetShedRecordStatus.GENERATING,
                waitingEndTime: Date.now() + 10 * 60 * 1000,
                chainType: CHAIN_TYPE_ENUM.ON_CHAIN,
              });
              getPetListData();
              fetchPetShedInfo();
            },
          },
        })
      );
    }
  };

  const handleConfirm = () => {
    if (canSubmit) {
      if (isBindSelected) {
        handlePetFusion();
      } else {
        handleChainPetFusion();
      }
    }
  };

  return (
    <div
      style={{
        display: 'flex',
        width: '100%',
        gap: '1.25rem',
        alignItems: 'stretch',
      }}>
      <StyledModalContent
        modalHeight="36.5rem"
        modalWidth="32.5rem"
        confirmText="Confirm"
        maxHeight="36.5rem"
        cancelText="Cancel"
        modalBodyPadding="0"
        footerStyle={{
          justifyContent: 'space-evenly',
          gap: '1rem',
          padding: '0',
          marginTop: 'auto',
        }}
        buttonStyle={{
          width: '12.5rem',
          height: '4rem',
        }}
        modalHeaderStyle={{
          top: '-2.9875rem',
        }}
        modalCloseBtnStyle={{
          right: '0.75rem',
          top: '35%',
        }}
        onConfirm={handleConfirm}
        onCancel={handleClose}
        onClose={handleClose}
        confirmDisabled={!canSubmit || fusionLoading}
        title={
          <PetBedManufactureTitleContainer>
            <PetBedManufactureTitle data-text={'Manufacture'}>Manufacture</PetBedManufactureTitle>
          </PetBedManufactureTitleContainer>
        }>
        <PetFusionContent />
      </StyledModalContent>
      <PetFusionRecipe />
    </div>
  );
};

const PetFusionModal = () => {
  const petFusionModalConfig = useAppSelector((state) => state.ModalReducer.petFusionModalConfig);

  return (
    <Dialog isOpen={petFusionModalConfig.isOpen} zIndex={98}>
      <PetFusionContextProvider>
        <PetFusionContainer />
      </PetFusionContextProvider>
    </Dialog>
  );
};

export default PetFusionModal;
