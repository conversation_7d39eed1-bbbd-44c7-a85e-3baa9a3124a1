import { AnimatePresence, motion } from 'framer-motion';
import styled, { css } from 'styled-components';
import { PreviewButton, PreviewButtonSvg } from './styles';
import SvgWrapper, { SpriteSvg } from '@/components/SvgWrapper';
import { useState } from 'react';
import BasicButton from '@/components/Basic/Button';
import { useAppSelector } from '@/hooks/useStore';

const OrderQueryBox = styled.div<{ $isHover?: boolean }>`
  display: flex;
  width: 6.125rem;
  height: 4rem;
  padding: 0.5rem 1.5rem;
  border: 0.0625rem solid transparent;
  flex-shrink: 0;

  background: transparent;
  align-items: center;
  border-radius: 1.5rem;
  gap: 1rem;
  transition:
    background 0.2s ease-in,
    width 0.1s ease-in,
    border-radius 0.1s ease-in;
  ${({ $isHover }) =>
    $isHover &&
    css`
      width: 29.125rem;
      max-height: 19rem;
      height: auto;
      padding: 0.5rem 0.5rem 0.5rem 1.5rem;
      justify-content: center;
      align-items: flex-end;
      gap: 1rem;
      border-radius: 1.5rem;
      border: 0.0625rem solid #a58061;
      background: #fff;
    `}
`;

const OrderQueryBoxItemWrapper = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-end;
`;

const OrderQueryList = styled(motion.div)`
  height: auto;
  max-height: 17.5rem;
  flex: 1;
  overflow-y: scroll;
  overflow-x: hidden;
  transition:
    padding 0.2s ease-in,
    height 0.2s ease-in;

  &::-webkit-scrollbar {
    background: transparent !important;
    cursor: pointer;
  }
  ::-webkit-scrollbar-track {
    background-color: transparent !important;
    cursor: pointer;
  }
  ::-webkit-scrollbar-thumb {
    cursor: pointer;
    border-radius: 0.5rem;
    background: #c69f7e !important;
  }
`;

const OrderQueryWrapper = styled(motion.div)<{ $isHover?: boolean }>`
  position: relative;
  z-index: 1;
  display: flex;
  justify-content: center;
  align-items: flex-end;
  cursor: pointer;
  position: absolute;
  left: -1.5rem;
  bottom: 50%;
  z-index: 11;
`;

const StyledWeb3QueryToastContainer = styled.div`
  width: 22.5rem;
  display: flex;
  height: 4rem;
  padding: 0.75rem 1rem;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;
  align-self: stretch;
  justify-content: space-between;
  align-items: center;
  & > span:nth-of-type(2) {
    color: #140f08;
    text-align: center;
    font-family: 'JetBrains Mono';
    font-size: 1rem;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    text-transform: capitalize;
  }
`;

const StyledLoadingIcon = styled(SvgWrapper)<{ $delay: number }>`
  width: 1.25rem;
  height: 1.25rem;
  color: #ff8316;
  --strokeColor: #ff8316;
  animation: spin 1.5s linear infinite -1s;
  animation-delay: ${({ $delay }) => -1 * $delay * 0.15}s;

  @keyframes spin {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }
`;

const StyledBasicButton = styled(BasicButton)`
  width: 6rem;
  height: 2.5rem;
  padding: 0 1.875rem;
  border-radius: 1rem;
  min-width: unset;
`;

const Web3QueryToast = ({ txid, delay }: { txid: string; delay: number }) => {
  const handleView = () => {
    window.open(`https://mempool.space/signet/tx/${txid}`, '_blank');
  };

  return (
    <StyledWeb3QueryToastContainer>
      <StyledLoadingIcon $delay={delay}>
        <SpriteSvg id="loading" />
      </StyledLoadingIcon>
      <span>Transaction Pending</span>
      <StyledBasicButton onClick={handleView}>View</StyledBasicButton>
    </StyledWeb3QueryToastContainer>
  );
};

const StyledPreviewButton = styled(PreviewButton)<{ $isHover: boolean }>`
  color: #ffffff;

  ${({ $isHover }) =>
    $isHover
      ? css`
          color: #ffc812;
          --strokeColor: #140f08;
        `
      : css`
          color: #ffffff;
        `}
  position:relative;
  &::after {
    content: attr(data-num);
    position: absolute;
    top: 0;
    right: 0;
    display: flex;
    width: 1.75rem;
    height: 1.75rem;
    padding: 0.625rem;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 0.625rem;
    flex-shrink: 0;
    border-radius: 3.125rem;
    background: #ff8316;
    box-shadow: 0 0.25rem 0 0 rgba(0, 0, 0, 0.25);
    color: #fff;
    font-family: Inter;
    font-size: 1rem;
    font-weight: 800;
    box-sizing: border-box;
    transform: translate(30%, -30%);
  }
`;

const OrderQuery = () => {
  const [isHovered, setIsHovered] = useState(false);
  const txidArr = useAppSelector((state) => state.PetOrderReducer.txidArr);
  if (txidArr.length === 0) return null;

  return (
    <>
      <OrderQueryWrapper
        onMouseLeave={() => setIsHovered(false)}
        onTouchEnd={() => setIsHovered(false)}
        $isHover={isHovered}>
        <OrderQueryBox $isHover={isHovered}>
          <StyledPreviewButton
            $isHover={isHovered}
            data-num={txidArr.length}
            onMouseEnter={() => setIsHovered(true)}
            onTouchStart={() => setIsHovered(true)}>
            <PreviewButtonSvg $spin>
              <SpriteSvg id="loading" />
            </PreviewButtonSvg>
          </StyledPreviewButton>
          <AnimatePresence>
            {isHovered && (
              <OrderQueryList
                key="OrderQueryListAnimation"
                initial={{ opacity: 0, y: '10rem' }}
                animate={{
                  opacity: 1,
                  y: '0rem',
                  transition: {
                    duration: 0.3,
                    ease: 'linear',
                  },
                }}
                exit={{
                  opacity: 0,
                  y: '10rem',
                  transition: {
                    duration: 0.3,
                    ease: 'linear',
                  },
                }}>
                <OrderQueryBoxItemWrapper>
                  {txidArr?.map((item, index) => {
                    return <Web3QueryToast key={item} txid={item} delay={index} />;
                  })}
                </OrderQueryBoxItemWrapper>
              </OrderQueryList>
            )}
          </AnimatePresence>
        </OrderQueryBox>
      </OrderQueryWrapper>
    </>
  );
};

export default OrderQuery;
