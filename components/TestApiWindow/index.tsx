import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { TestApiWindowView, ToggleButton } from './style';
import { ButlerUtil } from '@/game/Global/GlobalButlerUtil';
import GlobalSpaceEvent, { GlobalDataKey } from '@/game/Global/GlobalSpaceEvent';
import * as THREE from 'three';
import { useTwitterAuth } from '@/hooks/useTwitterAuth';
import { useDispatch, useSelector } from 'react-redux';
import { IAppState, IGameState } from '@/constant/type';
import toast from 'react-hot-toast';
import {
  setEasterEggInfo,
  setEasterEggReward,
  setFishEasterEggModal,
  setLeftTime,
  setRandomEventResult,
  setRockLeftTime,
  setTreeLeftTime,
  setUserBasicInfo,
  setWhackAMoleEasterEgg,
} from '@/store/app';
import { AppGameApiKey, GetMyPlayer } from '@/game/TSX/Character/MyPlayer';
import { useComboManager } from '../Combo/ComboManager';
import useRewards from '@/components/Rewards';
import { useFish } from '@/hooks/useFish';
import { getMaterialList, getUserDropItemList } from '@/server';
import { Events } from '@/utils/clientEvents';
import { setBagInventoryList } from '@/store/game';
import { ItemConfig, ItemType } from '@/game/Config/ItemConfig';
import CommunityTableModal, { CommunityTableModalRef } from '../CommunityTable';
import { ResourceTableModal, ResourceTableModalRef } from '../ResourceTable';
import Claim, { ClaimRef } from '../Claim';
import Submission, { SubmissionRef } from '../Submission';
import confetti from 'canvas-confetti';
import { useNetWork } from '@/game/TS/useNetWork';
import { ItemDropConfig } from '@/game/Config/ItemDropConfig';
import { getPizzaActivity } from '@/game/TS/Activity/PizzaActivity';
import SubmissionV2, { SubmissionV2Ref } from '../SubmisionV2';
import { TreeConfig } from '@/game/Config/TreeConfig';
import { IEasterEggRewardOpenConfig } from '../EasterEggReward';
import { usePlayerEnergyDispatch } from '@/contexts/playerEnergyContext';
import { playerEnergyZustandStore } from '@/contexts/playerEnergyContext/store';
import { updateModalState } from '@/store/modal';
import { PetShedConfig } from '@/game/Config/PetShedConfig';
import { useRetrievePet } from '@/hooks/useRetrievePickPet';
import usePetWork from '@/hooks/usePetWork';
import { StoneConfig } from '@/game/Config/StoneConfig';
import { EntityManager } from '@/game/TS/Entity/EntityManager';
import { ComponentType } from '@/game/TS/Entity/Enum';
import { EntityTransform } from '@/game/TS/Entity/Components/EntityTransform';
import { EntityAnimation } from '@/game/TS/Entity/Components/EntityAnimation';
import { RewardType } from '@/constant/enum';

// 示例字幕数据
const SAMPLE_SUBTITLES = [
  {
    start: 0,
    end: 3.8,
    content: '文件是一种常用于web视频字幕的文本格式。',
  },
  {
    start: 3.8,
    end: 7.6,
    content: '它的基本结构由两部分组成,时间戳和字幕文本。',
  },
  {
    start: 7.6,
    end: 13.6,
    content: '每个字幕段落由一个时间戳、字幕文本以及可能的其他设置如样式、位置等构成。',
  },
  {
    start: 13.6,
    end: 16.8,
    content: '以下是一个VTT文件的典型结构和实例。',
  },
];

// 示例音频URL
const SAMPLE_AUDIO_URL =
  'https://static.satworld.io/test_webm/c510a03bf1b943c0e28ec439b24e0d9f_zh_main.wav';

// 示例引用文件
const SAMPLE_CITATION_FILES = [
  {
    fileId: 'file-7rm5ySmMX4F6itwbPjZAcH',
    fileUrl: 'https://static.satworld.io/openai/1739521691993_temp.pdf',
    title: '深夜手机失去控制 信用卡被刷爆！警方提醒：这一功能赶紧关闭.pdf',
    webLink: 'https://baijiahao.baidu.com/s?id=1823991163793395911',
  },
  {
    fileId: 'file-7rm5ySmMX4F6itwbPjZAcH',
    fileUrl: 'https://static.satworld.io/openai/1739521691993_temp.pdf',
    title: '深夜手机失去控制 信用卡被刷爆！警方提醒：这一功能赶紧关闭.pdf',
    webLink: '',
  },
];

// 定义API类型
interface ApiAction {
  name: string;
  call: () => void;
  category?: string;
  loading?: boolean;
  disabled?: boolean;
}

export default function TestApiWindow() {
  const myPlayer = GetMyPlayer();
  const { isConnected } = useNetWork();
  // 添加显示/隐藏状态
  const [isVisible, setIsVisible] = useState<boolean>(true);
  const dispatch = useDispatch();
  // 添加按钮加载状态
  const [twitterLoading, setTwitterLoading] = useState(false);
  const [equipmentLoading, setEquipmentLoading] = useState(false);
  const [scoreLoading, setScoreLoading] = useState(false);
  const { showComboWithType, ComboDisplay } = useComboManager();
  const comboNumberRef = useRef(0);
  const { onCompleteFishEggTask } = useFish();
  const resourceTableModalRef = useRef<ResourceTableModalRef>(null);
  const claimRef = useRef<ClaimRef>(null);
  const submissionRef = useRef<SubmissionRef>(null);
  const submissionV2Ref = useRef<SubmissionV2Ref>(null);
  const [showFailed, setShowFailed] = useState(false);
  const { isOpen, setIsOpen, Rewards } = useRewards();
  const [debugLight, setDebugLight] = useState(false);
  const [fpsOpen, setFpsOpen] = useState(false);
  const [debugItemTransform, setDebugItemTransform] = useState(false);
  const { btcAddress, axeParams, treeList, userBasicInfo, btcWallet } = useSelector(
    (state: { AppReducer: IAppState }) => state.AppReducer
  );
  const { bagInventoryList, petShedInfo, petList } = useSelector(
    (state: { GameReducer: IGameState }) => state.GameReducer
  );
  const { retrievePet } = useRetrievePet();
  const {
    getSummonPetListStatusInAxe,
    getSummonPetListStatusInPickaxe,
    getSummonPetListStatusInFish,
    getSummonPetList,
    // handlePetAxe,
    // handlePetPickaxe,
    // handlePetFish,
    handleSocketPetAxe,
    handleSocketPetFishing,
    handleSocketPetPickaxe,
  } = usePetWork();

  const { startAuth, isLoading } = useTwitterAuth({
    onSuccess: (data) => {
      setTwitterLoading(false);
    },
    onError: (error) => {
      setTwitterLoading(false);
    },
  });
  const communityTableModalRef = useRef<CommunityTableModalRef>(null);
  // 切换显示/隐藏状态
  const toggleVisibility = () => {
    setIsVisible((prev) => !prev);
  };

  // 检查斧头耐久度是否为0
  const isAxeDurabilityZero = useMemo(() => {
    // 如果存在userItemId，但是totalDurability为0，则表示当前斧头耐久度为0，需要重新领取斧头，否则表示斧头使用中
    return !!axeParams?.userItemId && axeParams.currentDurability === 0;
  }, [axeParams]);

  // 处理领取装备
  const handleReceiveEquipment = useCallback(async () => {
    // 直接调用领取斧头的逻辑
    myPlayer.callAppApi(AppGameApiKey.receiveAxe);
  }, [myPlayer]);

  const startCooling = () => {
    dispatch(setLeftTime(10));
  };

  const startRule = () => {
    myPlayer.callAppApi(AppGameApiKey.activityRule);
  };

  const startCombo = (combo: number) => {
    comboNumberRef.current += combo;
    showComboWithType(comboNumberRef.current);
  };

  // useEffect(() => {
  //   if (dogEasterEgg && dogEasterEgg.length) {
  //     animationSequenceRef.current?.start();
  //   }
  // }, [dogEasterEgg]);

  useEffect(() => {
    const debugLight = localStorage.getItem('debugLightAndFog') === 'true';
    setDebugLight(debugLight);
    const fpsOpen = localStorage.getItem('fpsOpen') === 'true';
    setFpsOpen(fpsOpen);
    const debugItemTransform = localStorage.getItem('debugItemTransform') === 'true';
    setDebugItemTransform(debugItemTransform);
  }, []);

  // const { updatePlayerInfo } = usePlayerEnergy();
  const playerEnergyDispatch = usePlayerEnergyDispatch();

  const getPet = () => {
    const mockTag = PetShedConfig.getInstance().getPetShedConfigList()[0].tag;
    const petTagInfo = petShedInfo.find((item) => item.positionTag === mockTag);
    const slotRecordId = petTagInfo?.slotRecordId;
    if (slotRecordId) {
      retrievePet(slotRecordId);
    }
  };

  const handlePetAxeReport = async () => {
    const currentPetInAxeList = getSummonPetListStatusInAxe();

    if (currentPetInAxeList.length > 0) {
      const aliveTreeObjectIdList = TreeConfig.getInstance()
        .getAliveTree()
        .map((item) => ({ id: item.id, tag: item.tag }));

      console.log(
        '%c aliveTreeObjectIdList',
        'color: orange; font-size: 20px;',
        aliveTreeObjectIdList
      );

      // 简易mock 宠物目标id数据
      const mockPetWithTargetId = currentPetInAxeList
        .map((item, idx) => ({
          petId: item._id,
          targetId: aliveTreeObjectIdList[idx].id,
          tag: aliveTreeObjectIdList[idx].tag,
        }))
        .filter((item) => item.targetId);

      mockPetWithTargetId.map((item) => handleSocketPetAxe(item.tag, item.targetId, item.petId));
    }
  };
  const handlePetPickaxeReport = async () => {
    const currentPetInPickaxeList = getSummonPetListStatusInPickaxe();

    if (currentPetInPickaxeList.length > 0) {
      const aliveStoneObjectIdList = StoneConfig.getInstance()
        .getAliveStone()
        .map((item) => ({ id: item.id, tag: item.tag }));

      console.log(
        '%c aliveStoneObjectIdList',
        'color: orange; font-size: 20px;',
        aliveStoneObjectIdList
      );

      // 简易mock 宠物目标id数据
      const mockPetWithTargetId = currentPetInPickaxeList
        .map((item, idx) => ({
          petId: item._id,
          targetId: aliveStoneObjectIdList[idx].id,
          tag: aliveStoneObjectIdList[idx].tag,
        }))
        .filter((item) => item.targetId);

      mockPetWithTargetId.map((item) =>
        handleSocketPetPickaxe(item.tag, item.targetId, item.petId)
      );
    }
  };
  const handlePetFishReport = async () => {
    const currentPetInFishList = getSummonPetListStatusInFish();
    if (currentPetInFishList.length > 0) {
      const petIdList = currentPetInFishList.map((item) => item._id);
      petIdList.map((item) => handleSocketPetFishing(item));
    }
  };

  // 使用 useMemo 缓存 API 列表
  const apiList = useMemo(() => {
    // 获取宠物对象的辅助函数
    const getPetObject = (object: THREE.Object3D): THREE.Object3D => {
      const pet = object.userData.pet as THREE.Object3D;
      if (pet) {
        return getPetObject(pet);
      }
      return object;
    };

    const List = [
      {
        name: 'web3宠物生成弹窗',
        call: () => {
          dispatch(updateModalState({ petSubmitChainModalConfig: { isOpen: true } }));
        },
        category: 'web3宠物',
        loading: false,
        disabled: false,
      },
      {
        name: '宠物合成弹窗',
        call: () => {
          dispatch(
            updateModalState({
              petFusionModalConfig: {
                isOpen: true,
                configData: {
                  slotRecordId: '',
                },
              },
            })
          );
        },
        category: '宠物系统',
        loading: false,
        disabled: false,
      },
      {
        name: '宠物挖矿上报',
        call: () => {
          handlePetPickaxeReport();
        },
        category: '宠物系统',
        loading: false,
        disabled: false,
      },
      {
        name: '宠物钓鱼上报',
        call: () => {
          handlePetFishReport();
        },
        category: '宠物系统',
        loading: false,
        disabled: false,
      },
      {
        name: '宠物伐木上报',
        call: () => {
          handlePetAxeReport();
        },
        category: '宠物系统',
        loading: false,
        disabled: false,
      },

      {
        name: '宠物窝收获',
        call: () => {
          //
          getPet();
        },
        category: '宠物系统',
        loading: false,
        disabled: false,
      },
      {
        name: '宠物窝放置弹窗',
        call: () => {
          const mockTag = PetShedConfig.getInstance().getPetShedConfigList()[0].tag;
          dispatch(
            updateModalState({
              placePetModalConfig: {
                isOpen: true,
                positionTag: mockTag,
              },
            })
          );
        },
        category: '宠物系统',
        loading: false,
        disabled: false,
      },
      {
        name: '宠物窝合成弹窗',
        call: () => {
          dispatch(updateModalState({ petSynthesisModalConfig: { isOpen: true } }));
        },
        category: '宠物系统',
        loading: false,
        disabled: false,
      },
      {
        name: '宠物玩法介绍弹窗',
        call: () => {
          dispatch(updateModalState({ petDescModalOpenConfig: { isOpen: true } }));
        },
        category: '宠物系统',
        loading: false,
        disabled: false,
      },
      {
        name: '宠物窝玩法介绍弹窗',
        call: () => {
          dispatch(updateModalState({ petBedDescModalConfig: { isOpen: true } }));
        },
        category: '宠物系统',
        loading: false,
        disabled: false,
      },
      {
        category: '资源刷新倒计时',
        name: '树刷新',
        call: () => {
          myPlayer.callAppApi(AppGameApiKey.getDispatch, (dispatch: any) => {
            dispatch(setTreeLeftTime(30));
          });
        },
      },
      {
        category: '资源刷新倒计时',
        name: '石头刷新',
        call: () => {
          myPlayer.callAppApi(AppGameApiKey.getDispatch, (dispatch: any) => {
            dispatch(setRockLeftTime(30));
          });
        },
      },

      {
        category: '体力系统',
        name: '疲劳提示弹窗',
        call: () => {
          playerEnergyZustandStore.getState().showEnergyModal();
        },
        loading: false,
        disabled: false,
      },
      {
        category: '体力系统',
        name: '设置20s疲劳时间',
        call: () => {
          playerEnergyDispatch({
            type: 'UPDATE',
            payload: {
              freeTime: Date.now() + 20 * 1000,
            },
          });
        },
        loading: false,
        disabled: false,
      },
      {
        category: '体力系统',
        name: '增加体力',
        call: () => {
          playerEnergyDispatch({
            type: 'UPDATE',
            payload: (state) => {
              const { energy, ...rest } = state;
              return {
                ...rest,
                energy: energy + 10 > rest.totalEnergy ? rest.totalEnergy : energy + 10,
              };
            },
          });

          // updatePlayerInfo((state) => {
          //   const { energy, ...rest } = state;
          //   return {
          //     ...rest,
          //     energy: energy + 10 > rest.totalEnergy ? rest.totalEnergy : energy + 10,
          //   };
          // });
        },
        loading: false,
        disabled: false,
      },
      {
        category: '体力系统',
        name: '减少体力',
        call: () => {
          playerEnergyDispatch({
            type: 'UPDATE',
            payload: (state) => {
              const { energy, ...rest } = state;
              return {
                ...rest,
                energy: energy - 10 <= 0 ? 0 : energy - 10,
              };
            },
          });

          // updatePlayerInfo((state) => {
          //   const { energy, ...rest } = state;
          //   return {
          //     ...rest,
          //     energy: energy - 10 <= 0 ? 0 : energy - 10,
          //   };
          // });
        },
        loading: false,
        disabled: false,
      },
      {
        category: '彩蛋玩法',
        name: 'pizzaRush规则弹窗',
        call: () => {
          myPlayer.callAppApi(AppGameApiKey.activityRule, 2);
        },
        loading: false,
        disabled: false,
      },
      {
        category: '彩蛋玩法',
        name: '顺序砍树规则弹窗',
        call: () => {
          myPlayer.callAppApi(AppGameApiKey.activityRule, 4);
        },
      },
      {
        category: '彩蛋玩法',
        name: '打地鼠规则弹窗',
        call: () => {
          myPlayer.callAppApi(AppGameApiKey.activityRule, 3);
        },
      },
      {
        category: '彩蛋玩法',
        name: '顺序砍树倒计时',
        call: () => {
          myPlayer.callAppApi(AppGameApiKey.setOrderTreeTimeLeft, Date.now() + 35 * 1000, 1);
        },
      },
      {
        category: '彩蛋玩法',
        name: 'pizzaRush倒计时',
        call: () => {
          const pizzaActivity = getPizzaActivity();
          pizzaActivity.socketUpdateActivityData({} as any, true);
        },
      },
      {
        category: '彩蛋玩法',
        name: '奖励弹窗',
        call: () => {
          dispatch(
            setEasterEggReward({
              quantity: 1,
              tag: 'wangcai',
              eventType: '',
            })
          );
          myPlayer.callAppApi(AppGameApiKey.showEasterEggRewards, {
            modalStyleType: 'axe',
            subTitle: 'Chop Master',
          } as IEasterEggRewardOpenConfig);
        },
        loading: false,
        disabled: false,
      },
      {
        category: '彩蛋玩法',
        name: '奖励弹窗样式二',
        call: () => {
          dispatch(
            setEasterEggReward({
              quantity: 1,
              tag: 'potato',
              eventType: '',
            })
          );
          myPlayer.callAppApi(AppGameApiKey.showEasterEggRewards, {
            modalStyleType: 'pickaxes',
            subTitle: 'Tap & Stick Fun',
          } as IEasterEggRewardOpenConfig);
        },
        loading: false,
        disabled: false,
      },
      {
        name: '结算弹窗',
        call: async () => {
          const pizzaActivity = getPizzaActivity();
          pizzaActivity.testShowReward();
        },
        category: 'pizza活动',
        loading: false,
        disabled: false,
      },
      {
        name: '重置活动',
        call: async () => {
          const pizzaActivity = getPizzaActivity();
          pizzaActivity.socketUpdateActivityData({} as any, true);
        },
        category: 'pizza活动',
        loading: false,
        disabled: false,
      },
      {
        name: '测试报名',
        call: async () => {
          const pizzaActivity = getPizzaActivity();
          pizzaActivity.joinActivity(true);
        },
        category: 'pizza活动',
        loading: false,
        disabled: false,
      },
      {
        name: '检查活动',
        call: async () => {
          const pizzaActivity = getPizzaActivity();
          const activityData = pizzaActivity.getActivityData();
          const now = Date.now();
          if (now < activityData.startTime) {
            toast.success(
              '游戏开始还有' + Math.floor((activityData.startTime - now) / 1000) + '秒'
            );
            return;
          }
        },
        category: 'pizza活动',
        loading: false,
        disabled: false,
      },
      {
        name: '显示所有点',
        call: async () => {
          const pizzaActivity = getPizzaActivity();
          pizzaActivity.showAllPoint();
        },
        category: 'pizza活动',
        loading: false,
        disabled: false,
      },
      {
        name: '获取掉落物列表',
        call: async () => {
          interface IUserDropItem {
            dropItemTag: string; // 掉落物品Tag
            isPickedUp: boolean; // 是否已拾取
            tag: string; // 掉落位置tag
            quantity: number; // 掉落数量
          }

          const list = await getUserDropItemList();
          const { code, msg, data } = list.data;
          if (code === 1) {
            const dropItemList = data as IUserDropItem[];
            // console.log("dropItemList=====", dropItemList);
          } else {
            console.error(msg);
          }
        },
        category: '拾取资源',
        loading: false,
        disabled: false,
      },
      {
        name: '检查掉落物刷新',
        call: () => {
          const time = ItemDropConfig.getInstance().getLastRefreshTime() - Date.now();
          toast.success('刷新还需要等待=====' + Math.floor(time / 1000) + '秒');
        },
        category: '拾取资源',
        loading: false,
        disabled: false,
      },
      {
        name: '合成背包',
        call: () => {
          myPlayer.callAppApi(AppGameApiKey.openSynthesis, 'tool');
        },
        category: '背包',
        loading: false,
        disabled: false,
      },
      {
        name: '打开Rewards',
        call: () => {
          setIsOpen(true);
        },
        category: '社区',
        loading: false,
        disabled: false,
      },
      {
        name: '3D资源排行榜表格',
        call: () => {
          resourceTableModalRef.current?.open();
        },
        category: '社区',
        loading: false,
        disabled: false,
      },
      {
        name: '空投彩蛋',
        call: () => {
          dispatch(
            setRandomEventResult({
              quantity: 0.01,
            })
          );
        },
        category: '社区',
        loading: false,
        disabled: false,
      },
      {
        name: '提交资源到社区',
        call: () => {
          confetti({
            particleCount: 100,
            spread: 100,
            origin: { x: 0.4, y: 0.5 },
            zIndex: 2000,
            ticks: 200,
          });

          setTimeout(() => {
            // 延迟后触发第二个粒子效果
            confetti({
              particleCount: 100,
              spread: 100,
              origin: { x: 0.6, y: 0.8 },
              zIndex: 2000,
              ticks: 200,
            });
          }, 300); // 300毫秒的延迟，可以根据需要调整

          setTimeout(() => {
            submissionRef.current?.open(
              {
                wood: 100,
                stone: 100,
                fish: 100,
              },
              'potato'
            );
          }, 500);
        },
        category: '社区',
        loading: false,
        disabled: false,
      },
      {
        name: '社区表格',
        call: async () => {
          communityTableModalRef.current?.open();
        },
        category: '社区',
        loading: false,
        disabled: false,
      },
      {
        name: '领取空投',
        call: () => {
          claimRef.current?.open(
            {
              faucetAmount: '10.234',
              faucetType: 'potato',
              tickBalance: '103.234',
              tickIcon: '/image/potato.png',
            },
            'potato'
          );
        },
        category: '社区',
        loading: false,
        disabled: false,
      },
      {
        name: '获取材料列表',
        call: async () => {
          const res = await getMaterialList();
          const { code, msg, data } = res.data;
          if (code === 1) {
          } else {
            console.error(msg);
          }
        },
        category: '背包',
        loading: false,
        disabled: false,
      },
      {
        name: '切换下一把斧头',
        call: () => {
          ItemConfig.getInstance().testHandItem(ItemType.Axe, bagInventoryList, (newList) => {
            dispatch(setBagInventoryList(newList));
          });
        },
        category: '背包',
        loading: false,
        disabled: false,
      },
      {
        name: '切换下一把稿子',
        call: () => {
          ItemConfig.getInstance().testHandItem(ItemType.Pickaxe, bagInventoryList, (newList) => {
            dispatch(setBagInventoryList(newList));
          });
        },
        category: '背包',
        loading: false,
        disabled: false,
      },
      {
        name: '切换下一把鱼竿',
        call: () => {
          ItemConfig.getInstance().testHandItem(
            ItemType.FishingRod,
            bagInventoryList,
            (newList) => {
              dispatch(setBagInventoryList(newList));
            }
          );
        },
        category: '背包',
        loading: false,
        disabled: false,
      },
      {
        name: '采集钓鱼材料动画',
        call: () => {
          Events.emitItemCollected('/image/yu.png', 1);
        },
        category: '背包',
        loading: false,
        disabled: false,
      },
      {
        name: '采集木头动画',
        call: () => {
          Events.emitItemCollected('/image/t2-1.png', 1);
        },
        category: '背包',
        loading: false,
        disabled: false,
      },
      {
        name: '采集矿石动画',
        call: () => {
          Events.emitItemCollected('/image/t2-2.png', 1);
        },
        category: '背包',
        loading: false,
        disabled: false,
      },
      {
        name: '甩竿',
        call: async () => {
          myPlayer.callAppApi(AppGameApiKey.useFishingRod);
        },
        category: '钓鱼活动',
        loading: false,
        disabled: false,
      },

      {
        name: '上报积分',
        call: () => {
          myPlayer.callAppApi(AppGameApiKey.fishingSuccess);
        },
        category: '钓鱼活动',
        loading: false,
        disabled: false,
      },
      {
        name: '钓鱼彩蛋入口',
        call: () => {
          dispatch(
            setEasterEggInfo([
              {
                domainName: '3.uniworlds',
                isSuccess: true,
              },
              {
                domainName: '1.uniworlds',
                isSuccess: true,
              },
              {
                domainName: '38548.uniworlds',
                isSuccess: true,
              },
              {
                domainName: '2.uniworlds',
                isSuccess: false,
              },
              {
                domainName: '4.uniworlds',
                isSuccess: false,
              },
            ])
          );
        },
        category: '钓鱼活动',
        loading: false,
        disabled: false,
      },
      {
        name: '完成彩蛋任务',
        call: () => {
          onCompleteFishEggTask();
        },
        category: '钓鱼活动',
        loading: false,
        disabled: false,
      },
      {
        name: '钓鱼彩蛋进度弹窗',
        call: () => {
          dispatch(setFishEasterEggModal(true));
        },
        category: '钓鱼活动',
        loading: false,
        disabled: false,
      },
      {
        name: '打开胜利动画',
        call: () => {
          // setShowVictory(true);
        },
        category: '挖矿活动',
        loading: false,
        disabled: false,
      },
      {
        name: '狗头设置mock数据',
        call: () => {
          // setShowVictory(true);
          const now = new Date().getTime();
          const baseConfig = {
            interval: 1000,
            pause: 1000,
            now: now,
          };

          const generateConfig = (number: number) => {
            const arr = new Array(number).fill(0);
            const resArr = arr.map((_, index, array) => {
              // const startTime = now + index * 1000;
              const interval = baseConfig.interval;
              if (index === 0) {
                const startTime = baseConfig.now + interval * (index + 1);
                const endTime = startTime + baseConfig.pause;
                return {
                  tag: 1,
                  interval: baseConfig.interval,
                  pause: baseConfig.pause,
                  startTime: startTime,
                  endTime: endTime,
                };
              } else {
                const startTime = baseConfig.now + interval * index + baseConfig.pause;
                const endTime = startTime + baseConfig.pause;

                return {
                  tag: index + 1,
                  interval: baseConfig.interval,
                  pause: baseConfig.pause,
                  startTime: startTime,
                  endTime: endTime,
                };
              }
            });
            return resArr;
          };
          const res = generateConfig(12);
          dispatch(
            setWhackAMoleEasterEgg({
              rewardType: 'potato' as any,
              ruleInfo: res,
            })
          );
        },
        category: '挖矿活动',
        loading: false,
        disabled: false,
      },
      {
        name: '狗头彩蛋弹窗',
        call: () => {
          myPlayer.callAppApi(AppGameApiKey.startDogEgg);
        },
        category: '挖矿活动',
        loading: false,
        disabled: false,
      },
      {
        name: isAxeDurabilityZero
          ? '领取新斧头'
          : axeParams?.userItemId
            ? '斧头使用中'
            : '领取斧头',
        call: handleReceiveEquipment,
        category: '砍树活动',
        loading: equipmentLoading,
        // disabled: axeParams?.userItemId && axeParams?.totalDurability > 0, // 如果有斧头且耐久度大于0，则禁用按钮
      },
      {
        name: '冷却期50s',
        call: startCooling,
        category: '砍树活动',
        loading: false,
        disabled: false,
      },
      {
        name: 'mock 设置数据',
        call: () => {
          const aliveTrees = TreeConfig.getInstance().getAliveTreeTagId();
          const rule = {
            rule: aliveTrees.join(','),
            // rule: '2002,2018,2006,2005,2017,2015,2012,2004,2009,2016,2008,2014,2010,2001,2011,2007',
            // rule: '2002,2018,2013,2006,2005,2017,2015,2012,2004,2009,2016,2008,2014,2010,2001,2011,2007,2003',
          };

          TreeConfig.getInstance().updateOrderTreeData(rule as any);
          // 唤起玩法规则提示弹窗
          myPlayer.callAppApi(AppGameApiKey.activityRule, 4);
        },
        category: '砍树活动',
        loading: false,
        disabled: false,
      },
      {
        name: '活动规则说明',
        call: startRule,
        category: '砍树活动',
        loading: false,
        disabled: false,
      },
      {
        name: 'Combo连击',
        call: () => startCombo(1),
        category: '砍树活动',
        loading: false,
        disabled: false,
      },
      {
        name: '领取结算',
        call: () => {
          const name = 'potato';
          const quantity = 100;
          myPlayer.callAppApi(AppGameApiKey.showRewards, name, quantity);
        },
        category: '砍树活动',
        loading: false,
        disabled: false,
      },
      {
        name: '测试斧子上限刷新(5s)',
        call: () => {
          if (userBasicInfo) {
            const ttl = 5;
            myPlayer.refreshTimeStamp = 0;
            setTimeout(() => {
              dispatch(
                setUserBasicInfo({
                  ...userBasicInfo,
                })
              );
            });
          }
        },
        category: '砍树活动',
        loading: false,
        disabled: false,
      },
      // 播报相关API
      {
        name: '更新播报链接',
        call: () => {
          ButlerUtil.updateBroadcast(SAMPLE_SUBTITLES, SAMPLE_AUDIO_URL, true);
        },
        category: '播报',
        loading: false,
        disabled: false,
      },

      // 管家控制相关API
      {
        name: '更新管家数据',
        call: () => ButlerUtil.updateButlerTransform(),
        category: '管家',
        loading: false,
        disabled: false,
      },
      {
        name: '隐藏管家',
        call: () => ButlerUtil.hideButler(),
        category: '管家',
        loading: false,
        disabled: false,
      },
      {
        name: '显示访客出身点',
        call: () => ButlerUtil.updateVisitorTransform(true),
        category: '管家',
        loading: false,
        disabled: false,
      },

      // 玩家控制相关API
      {
        name: '玩家虚弱10s',
        call: () => {
          playerEnergyZustandStore.getState().updatePlayerInfo({
            freeTime: Date.now() + 10 * 1000,
          });
        },
        category: '玩家',
        loading: false,
        disabled: false,
      },
      {
        name: '打印玩家坐标',
        call: () => {
          toast.success(
            myPlayer.position.x.toFixed(2) +
              ',' +
              myPlayer.position.y.toFixed(2) +
              ',' +
              myPlayer.position.z.toFixed(2)
          );
        },
        category: '玩家',
        loading: false,
        disabled: false,
      },
      {
        name: '增加pizza',
        call: () => {
          if (myPlayer.pizzaCount === -1) {
            myPlayer.startPizza(RewardType.potato);
          } else {
            myPlayer.addPizza();
          }
        },
        category: '玩家',
        loading: false,
        disabled: false,
      },
      {
        name: '清空pizza',
        call: () => {
          myPlayer.removePizza();
        },
        category: '玩家',
        loading: false,
        disabled: false,
      },
      {
        name: '创建连接玩家',
        call: () => {
          if (isConnected()) {
            const entity = EntityManager.getInstance().createClientEntity([
              ComponentType.Transform,
              ComponentType.AvatarMesh,
              ComponentType.Face,
              ComponentType.Animation,
            ]);
            entity.name = 'test player';
            const transform = entity.getComponent<EntityTransform>(ComponentType.Transform);
            transform?.setPosition(myPlayer.position, myPlayer.quaternion);
            const animation = entity.getComponent<EntityAnimation>(ComponentType.Animation);
            animation?.playAnimation('Action_00');
            animation?.setReplaceAnimations(['Action_00']);
          }
        },
        category: '玩家',
        loading: false,
        disabled: false,
      },
      {
        name: '开启自由视角',
        call: () => {
          GlobalSpaceEvent.SetDataValue<boolean>(GlobalDataKey.OpenFreeCamera, true);
        },
        category: '视角',
        loading: false,
        disabled: false,
      },
      {
        name: '关闭自由视角',
        call: () => {
          GlobalSpaceEvent.SetDataValue<boolean>(GlobalDataKey.OpenFreeCamera, false);
        },
        category: '视角',
        loading: false,
        disabled: false,
      },

      // 宠物相关API
      {
        name: '创建一个宠物',
        call: () => undefined,
        category: '宠物',
        loading: false,
        disabled: false,
      },
      {
        name: '清空宠物',
        call: () => undefined,
        category: '宠物',
        loading: false,
        disabled: false,
      },
    ];
    const testList = [
      {
        name: debugLight ? '关闭光源调试' : '打开光源调试',
        call: async () => {
          localStorage.setItem('debugLightAndFog', String(!debugLight));
          //重启
          window.location.reload();
        },
        category: '调试',
        loading: false,
        disabled: false,
      },
      {
        name: fpsOpen ? '关闭FPS' : '打开FPS',
        call: async () => {
          localStorage.setItem('fpsOpen', String(!fpsOpen));
          //重启
          window.location.reload();
        },
        category: '调试',
        loading: false,
        disabled: false,
      },
      {
        name: debugItemTransform ? '关闭手持物调试' : '打开手持物调试',
        call: async () => {
          localStorage.setItem('debugItemTransform', String(!debugItemTransform));
          //重启
          window.location.reload();
        },
        category: '调试',
        loading: false,
        disabled: false,
      },
      {
        name: '创建特效',
        call: async () => {
          myPlayer.callAppApi(AppGameApiKey.createParticle, myPlayer.position);
        },
        category: '调试',
        loading: false,
        disabled: false,
      },
      {
        name: '刷新cdn缓存',
        call: async () => {
          localStorage.removeItem(`cdn_tset_version`);
          window.location.reload();
        },
        category: '调试',
        loading: false,
        disabled: false,
      },
    ];
    List.forEach((item: any) => {
      testList.push(item);
    });
    return testList;
  }, [
    debugLight,
    fpsOpen,
    debugItemTransform,
    twitterLoading,
    isLoading,
    handleReceiveEquipment,
    equipmentLoading,
    scoreLoading,
    axeParams,
    btcAddress,
    treeList,
    isAxeDurabilityZero,
    petShedInfo,
    petList,
  ]);

  // 按类别分组API
  const groupedApis = useMemo(() => {
    const groups: Record<string, ApiAction[]> = {};

    apiList.forEach((api: any) => {
      const category = api.category || '其他';
      if (!groups[category]) {
        groups[category] = [];
      }
      groups[category].push(api);
    });

    return groups;
  }, [apiList]);

  return (
    <>
      {/* 显示/隐藏开关按钮 */}
      <ToggleButton onClick={toggleVisibility} isVisible={isVisible}>
        {isVisible ? '隐藏工具' : '显示工具'}
      </ToggleButton>

      {/* API窗口 */}
      {isVisible && (
        <TestApiWindowView>
          <div className="window-header">
            <h2>API测试工具</h2>
            <button className="close-button" onClick={toggleVisibility}>
              ×
            </button>
          </div>

          {Object.entries(groupedApis).map(([category, apis]) => (
            <div key={category} className="api-category">
              <h3>{category}</h3>
              <div className="api-buttons">
                {apis.map((api, index) => (
                  <button
                    key={`${category}-${index}`}
                    onClick={api.call}
                    className={`api-button ${api.loading ? 'loading' : ''}`}
                    disabled={api.loading || api.disabled}>
                    {api.loading ? `${api.name}中...` : api.name}
                  </button>
                ))}
              </div>
            </div>
          ))}
        </TestApiWindowView>
      )}
      {ComboDisplay && <ComboDisplay />}
      <ResourceTableModal
        ref={resourceTableModalRef}
        onClose={() => undefined}
        resourceType="stone"
      />
      <Claim ref={claimRef} onClose={() => undefined} />
      <Submission ref={submissionRef} onClose={() => undefined} />
      <CommunityTableModal ref={communityTableModalRef} onClose={() => undefined} />
      <SubmissionV2 ref={submissionV2Ref} />
      {isOpen && <Rewards />}
    </>
  );
}
