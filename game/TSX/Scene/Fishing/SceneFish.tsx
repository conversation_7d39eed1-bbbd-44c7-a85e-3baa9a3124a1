import { useEffect, useRef, useState } from 'react';
import * as THREE from 'three';
import { Quaternion } from 'three';
import { useFrame } from '@react-three/fiber';
import { getParticleSystem } from '@/game/TSX/Particles/ParticleSystem';
import { UseGameState } from '@/game/Lib/stores/useGame';
import { useAnimations } from '@react-three/drei';
import { ConfigManager } from '@/game/Config/ConfigManager';
import { ComponentType } from '@/game/TS/Entity/Enum';
import { EntityTransform } from '@/game/TS/Entity/Components/EntityTransform';
import { EntityAnimation } from '@/game/TS/Entity/Components/EntityAnimation';
import { EntityManager } from '@/game/TS/Entity/EntityManager';
import { EntityGlbMesh } from '@/game/TS/Entity/Components/EntityGlbMesh';
import { Entity } from '@/game/TS/Entity/Entity';

class ParabolaTrajectory {
  private start: THREE.Vector3;
  private end: THREE.Vector3;
  private height: number;

  private a = 0;
  private xv = 0;
  private yv = 0;

  constructor(startPoint: THREE.Vector3, endPoint: THREE.Vector3, height: number) {
    this.start = startPoint;
    this.end = endPoint;
    this.height = height;

    // 计算抛物线参数
    this.calculateCoefficients();
  }

  calculateCoefficients() {
    // 解抛物线方程 y = ax² + bx + c
    // 经过起点 (x1,y1), 顶点 (xv, yv), 终点 (x2,y2)
    const x1 = this.start.x;
    const y1 = this.start.y;
    const x2 = this.end.x;
    const y2 = this.end.y;

    // 假设顶点在中间x位置，y为起点y + 高度
    const xv = (x1 + x2) / 2;
    const yv = Math.max(y1, y2) + this.height;

    // 使用顶点式 y = a(x - xv)² + yv
    // 代入起点求a
    this.a = (y1 - yv) / ((x1 - xv) * (x1 - xv));
    this.xv = xv;
    this.yv = yv;
  }

  getPosition(progress: number) {
    // progress 0到1之间的值
    const x = this.start.x + (this.end.x - this.start.x) * progress;
    const y = this.a * Math.pow(x - this.xv, 2) + this.yv;

    // 如果是3D空间，可以添加z坐标的线性插值
    const z = this.start.z + (this.end.z - this.start.z) * progress;

    return new THREE.Vector3(x, y, z);
  }
}

export default function SceneFish({
  fishUrl,
  fishScale,
  initPos,
  targetPos,
  facePos,
}: {
  fishUrl: string;
  fishScale: number;
  initPos: THREE.Vector3;
  targetPos: THREE.Vector3;
  facePos: THREE.Vector3;
}) {
  const particleSystem = getParticleSystem();
  const groupRef = useRef<THREE.Group>(null);
  const [trajectory, setTrajectories] = useState<ParabolaTrajectory | null>(null);
  const fishFlyTime = useRef(0);
  const fishFlySpeed = useRef(1);
  const [fishEntity, setFishEntity] = useState<Entity | null>(null);

  useEffect(() => {
    const entity = EntityManager.getInstance().createClientEntity([
      ComponentType.Transform,
      ComponentType.Animation,
      ComponentType.GlbMesh,
    ]);
    entity.name = 'fish';
    const glbMeshComponent = entity.getComponent<EntityGlbMesh>(ComponentType.GlbMesh);
    if (glbMeshComponent) {
      glbMeshComponent.setGlbUrl(fishUrl);
      glbMeshComponent.setScale(fishScale);
    }
    const transformComponent = entity.getComponent<EntityTransform>(ComponentType.Transform);
    if (transformComponent) {

    }
    const animationComponent = entity.getComponent<EntityAnimation>(ComponentType.Animation);
    if (animationComponent) {
      animationComponent.setReplaceAnimations(['Action_fish_00', 'Action_fish_01']);
      animationComponent.setDefaultAnimation('Action_fish_00');
      animationComponent.playAnimation('Action_fish_00');
      setTimeout(() => {
        animationComponent.playAnimation('Action_fish_01');
      }, 800);
    }
    setFishEntity(entity);
    return () => {
      EntityManager.getInstance().removeEntity(entity);
    };
  }, [fishUrl, fishScale]);

  useEffect(() => {
    if (targetPos) {
      particleSystem.addParticle(
        initPos.clone(),
        new Quaternion(),
        './particles/Effect_water_splash.json',
        2,
        3000
      );
      initPos.y = targetPos.y;
      setTrajectories(new ParabolaTrajectory(initPos, targetPos, 2));
      fishFlyTime.current = Date.now();
      ConfigManager.getInstance().getData((data) => {
        fishFlySpeed.current = data.fish_fly_speed;
      });
    }
    return () => {
      setTrajectories(null);
    };
  }, [initPos, particleSystem, targetPos]);

  useFrame((state) => {
    if (groupRef.current && trajectory && fishFlyTime.current > 0) {
      // 没有加载完成

      const progress = ((Date.now() - fishFlyTime.current) / 1000) * fishFlySpeed.current;
      const position = trajectory.getPosition(Math.min(progress, 1));
      const positionNext = trajectory.getPosition(Math.min(progress + 0.01, 1));
      groupRef.current.position.copy(position);
      if (position.y < positionNext.y) {
        groupRef.current.lookAt(positionNext);
      } else {
        facePos.y = position.y;
        groupRef.current.lookAt(facePos);
      }
      if (progress >= 1) {
        setTrajectories(null);
      }
      if (fishEntity) {
        const transformComponent = fishEntity.getComponent<EntityTransform>(
          ComponentType.Transform
        );
        if (transformComponent) {
          transformComponent.setPosition(groupRef.current.position, groupRef.current.quaternion);
        }
      }
    }
  });

  return <group ref={groupRef}></group>;
}
