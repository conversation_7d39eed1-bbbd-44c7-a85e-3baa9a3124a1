import React, { useEffect, useRef, useState } from 'react';
import * as THREE from 'three';
import { GLTF } from 'three-stdlib';
import createUseGame, { UseGameState } from '@/game/Lib/stores/useGame';
import { useAnimations } from '@react-three/drei';
import { ItemDropConfig, ItemDropData } from '@/game/Config/ItemDropConfig';
import { LoaderUtil } from '@/game/TSX/Util/LoaderUtil';
import { AppGameApiKey, GetMyPlayer } from '@/game/TSX/Character/MyPlayer';
import GlobalSpace, { GAME_OP_TYPE } from '@/game/Global/GlobalSpace';
import { getCdnLink } from '@/AvatarOrdinalsBrowser/utils';

function ItemDropAnimation({
  root,
  useGame,
  animations,
}: {
  animations: THREE.AnimationClip[];
  root: THREE.Object3D;
  useGame: any;
}) {
  /**
   * Character animations setup
   */
  const curAnimation = useGame((state: UseGameState) => state.curAnimation);
  const setCurAnimation = useGame((state: UseGameState) => state.setCurAnimation);

  const { actions } = useAnimations(animations, root);

  const animationSet = useGame((state: UseGameState) => state.animationSet);

  useEffect(() => {
    if (animations.length === 0 || curAnimation === null || curAnimation === undefined) {
      return;
    }
    const actionList: string[] = curAnimation.split('|');

    const actionKey = actionList[0];
    const actionSpeed = Number(actionList[1] || 1);

    const finishCall = () => {
      setCurAnimation('idle');
    };
    // Play animation
    const action: THREE.AnimationAction | null = actions[actionKey || ''];
    if (action) {
      action.timeScale = actionSpeed;

      action.reset().fadeIn(0.2).play();

      // When any action is clamp and finished reset animation
      (action as any)._mixer.addEventListener('finished', () => finishCall());
    }
    return () => {
      if (action) {
        // Fade out previous action
        action.fadeOut(0.2);

        // Clean up mixer listener, and empty the _listeners array
        (action as any)._mixer.removeEventListener('finished', () => finishCall());
        (action as any)._mixer._listeners = [];
      }
    };
  }, [actions, curAnimation, animationSet, animations]);

  return null;
}

export default function SceneItemDrop({ dropId }: { dropId: number }) {
  const myPlayer = GetMyPlayer();
  const myUseGame = myPlayer.getUseGame();
  const groupRef = useRef<THREE.Group>(null);
  const useGameRef = useRef(createUseGame());
  const [dropData, setDropData] = useState<ItemDropData | null>(null);
  const setMyCurAnimation = myUseGame((state: UseGameState) => state.setCurAnimation);
  const curAnimation = myUseGame((state: UseGameState) => state.curAnimation);
  const setCurAnimation = useGameRef.current((state: UseGameState) => state.setCurAnimation);
  const [animations, setAnimations] = useState<THREE.AnimationClip[]>([]);
  const [nearItem, setNearItem] = useState<boolean>(false);
  const [gltf, setGltf] = useState<GLTF | null>(null);
  const [isPickedUp, setIsPickedUp] = useState<boolean>(true);
  const [clickDelay, setClickDelay] = useState<boolean>(false);

  const timer = useRef<any>(0);

  useEffect(() => {
    ItemDropConfig.getInstance().getData(dropId, (data) => {
      setDropData(data);
    });
  }, []);

  useEffect(() => {
    if (curAnimation !== 'Action_29') {
      clearTimeout(timer.current);
      setClickDelay(false);
    }
  }, [curAnimation]);

  useEffect(() => {
    if (dropData && !isPickedUp) {
      setCurAnimation(dropData.actionName);
      LoaderUtil.loadGlb(dropData.glbUrl, (gltf) => {
        if (groupRef.current) {
          groupRef.current.position.copy(
            new THREE.Vector3(dropData.position[0], dropData.position[1], dropData.position[2])
          );
          groupRef.current.quaternion.setFromEuler(
            new THREE.Euler(
              (dropData.rotation[0] * Math.PI) / 180,
              (dropData.rotation[1] * Math.PI) / 180,
              (dropData.rotation[2] * Math.PI) / 180
            )
          );
          gltf.scene.scale.set(dropData.glbScale, dropData.glbScale, dropData.glbScale);
          groupRef.current.add(gltf.scene);
        }
        setGltf(gltf);
        setAnimations(gltf.animations);
      });
    }

    return () => {
      if (groupRef.current) {
        groupRef.current.clear();
      }
    };
  }, [dropData, isPickedUp]);

  useEffect(() => {
    if (dropData && !isPickedUp && !clickDelay) {
      const opList: string[] = [];
      if (nearItem) {
        opList.push(
          GlobalSpace.addGameOp(
            GAME_OP_TYPE.CustomOp,
            () => {
              setClickDelay(true);
              setMyCurAnimation('Action_29');
              myPlayer.facePosition(
                new THREE.Vector3(dropData.position[0], dropData.position[1], dropData.position[2])
              );
              timer.current = setTimeout(() => {
                setClickDelay(false);
                dropData.isPickedUp = true;
                setIsPickedUp(true);
                myPlayer.callAppApi(AppGameApiKey.pickUpDrop, dropData);
              }, 1000);
            },
            0,
            'Pick Up',
            getCdnLink('./icon/option/pickUp.svg')
          )
        );
      }
      return () => {
        opList.forEach((op) => {
          GlobalSpace.removeGameOp(op);
        });
      };
    }
  }, [dropData, isPickedUp, nearItem, clickDelay]);

  useEffect(() => {
    const pointKey = 'drop_' + dropId;
    if (dropData) {
      myPlayer.registerMapPoint(
        pointKey,
        new THREE.Vector3(dropData.position[0], dropData.position[1], dropData.position[2]),
        (distance) => {
          setNearItem(distance < dropData.distance);
        }
      );
      const interval = setInterval(() => {
        setIsPickedUp(dropData.isPickedUp);
      }, 500);
      return () => {
        clearInterval(interval);
        myPlayer.unregisterMapPoint(pointKey);
      };
    }
  }, [dropData]);

  return (
    <group ref={groupRef}>
      {gltf && (
        <ItemDropAnimation animations={animations} root={gltf.scene} useGame={useGameRef.current} />
      )}
    </group>
  );
}
