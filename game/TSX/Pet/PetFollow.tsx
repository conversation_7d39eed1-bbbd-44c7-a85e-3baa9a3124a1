/*
Auto-generated by: https://github.com/pmndrs/gltfjsx
*/

import * as THREE from 'three';
import { Entity } from '@/game/TS/Entity/Entity';
import { AnimationType, PetFollowData } from '@/game/TSX/Pet/PetEntity';
import { useFrame, useThree } from '@react-three/fiber';
import { EntityWalkPoint } from '@/game/TS/Entity/Components/EntityWalkPoint';
import { ComponentType } from '@/game/TS/Entity/Enum';
import { useEffect, useMemo, useRef, useState } from 'react';
import { EntityAnimation } from '@/game/TS/Entity/Components/EntityAnimation';
import { Raycaster } from 'three/src/Three.Core';
import GlobalSpaceEvent, { GlobalDataKey } from '@/game/Global/GlobalSpaceEvent';

function PetFollow({ petEntity, followData }: { petEntity: Entity; followData: PetFollowData }) {
  const { scene } = useThree();
  const [sceneLoading, setSceneLoading] = useState<boolean>(true);
  const stopList = useMemo(() => {
    if (sceneLoading) return [];
    const list: THREE.Object3D[] = [];
    scene.traverse((value) => {
      if (value.type === 'Mesh' && value.name === 'stop') {
        list.push(value);
      }
    });
    return list;
  }, [scene, sceneLoading]);
  const raycasterRef = useRef<Raycaster>(new Raycaster());
  const { curPosition, followSlot, followPositions } = followData;
  const [path, setPath] = useState<THREE.Vector3[]>([]);
  const pathIndexRef = useRef(0);

  const findGroundPosition = (position: THREE.Vector3) => {
    // 从中心点到生成的点发射射线
    const startPoint = position.clone();
    startPoint.y += 2;
    raycasterRef.current.set(startPoint, new THREE.Vector3(0, -1, 0));
    raycasterRef.current.far = 4; //多检测1的长度 , 给出生点留出体积
    const intersects = raycasterRef.current.intersectObjects(stopList);
    if (intersects.length > 0) {
      return intersects[0].point;
    }
    return position;
  };

  const getRandomOffset = (oldPosition: THREE.Vector3) => {
    const offsetList = [
      new THREE.Vector3(0.5, 0, 0),
      new THREE.Vector3(-0.5, 0, 0),
      new THREE.Vector3(0, 0, 0.5),
      new THREE.Vector3(0, 0, -0.5),
    ];
    const offset = offsetList[Math.floor(Math.random() * offsetList.length)];
    return oldPosition.clone().add(offset);
  };

  useEffect(() => {
    const loadingKey = GlobalSpaceEvent.ListenKeyDataChange<boolean>(
      GlobalDataKey.SceneLoading,
      (value) => {
        setSceneLoading(value);
      }
    );

    return () => {
      GlobalSpaceEvent.RemoveListener(GlobalDataKey.SceneLoading, loadingKey);
    };
  }, []);

  useEffect(() => {
    if (!petEntity || path.length == 0) return;
    const animation = petEntity.getComponent<EntityAnimation>(ComponentType.Animation);
    const nextPoint = () => {
      animation?.playAnimation(AnimationType.Walk);
      const walkPoint = petEntity.getComponent<EntityWalkPoint>(ComponentType.WalkPoint);
      walkPoint?.setTarget(findGroundPosition(path[pathIndexRef.current]));
      // walkPoint?.setTarget(path[pathIndexRef.current]);
      walkPoint?.setArriveCallback((isTransform: boolean) => {
        if (pathIndexRef.current == path.length - 1) {
          animation?.playAnimation(AnimationType.Idle);
          return;
        }
        pathIndexRef.current = pathIndexRef.current + 1;
        nextPoint();
      });
    };
    nextPoint();
  }, [petEntity, path]);

  const updatePath = (from: THREE.Vector3, to: THREE.Vector3) => {
    if (!petEntity) return;
    const distance = from.distanceTo(to);
    const normal = to.clone().sub(from).normalize();
    const target1 = from.clone().add(normal.clone().multiplyScalar(distance / 2));
    const target2 = from.clone().add(normal.clone().multiplyScalar(distance));
    pathIndexRef.current = 0;
    setPath([target1, target2]);
  };

  useFrame(() => {
    if (!petEntity) return;
    const walkPoint = petEntity.getComponent<EntityWalkPoint>(ComponentType.WalkPoint);
    if (!walkPoint) return;
    if (walkPoint.position) {
      curPosition.copy(walkPoint.position);
      const targetPosition = followPositions[followSlot - 1];
      // console.log('targetPosition', targetPosition);
      if (path.length > 0) {
        if (pathIndexRef.current > 0) {
          const endPosition = path[path.length - 1];
          const distance = endPosition.distanceTo(targetPosition);
          //距离上一次寻路的终点大于2,重新寻路
          if (distance > 2) {
            updatePath(walkPoint.position, getRandomOffset(targetPosition));
          }
        }
      } else {
        //没有寻路路径,重新寻路
        updatePath(walkPoint.position, getRandomOffset(targetPosition));
      }
    }
  });
  return null;
}

export default PetFollow;
