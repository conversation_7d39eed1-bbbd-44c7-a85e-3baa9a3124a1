/*
Auto-generated by: https://github.com/pmndrs/gltfjsx
*/

import * as THREE from 'three';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import createUseGame, { UseGameState } from '@/game/Lib/stores/useGame';
import { useFrame } from '@react-three/fiber';
import { PetConfig } from '@/game/Config/PetConfig';
import useLatest from '@/hooks/useLatest';
import { Entity } from '@/game/TS/Entity/Entity';
import { EntityManager } from '@/game/TS/Entity/EntityManager';
import { ComponentType } from '@/game/TS/Entity/Enum';
import { EntityTopText } from '@/game/TS/Entity/Components/EntityTopText';
import { EntityAnimation } from '@/game/TS/Entity/Components/EntityAnimation';
import { EntityGlbMesh } from '@/game/TS/Entity/Components/EntityGlbMesh';
import { EntityTransform } from '@/game/TS/Entity/Components/EntityTransform';
import { useAppSelector } from '@/hooks/useStore';
import Ecctrl from '@/game/Lib/Ecctrl';

export interface FollowPetItem {
  serverId: string;
  petTag: string;
  curPosition: THREE.Vector3;
  targetPosition: THREE.Vector3 | null;
  followSlot: number;
  petName: string;
  topIconName: string;
}
let index = 0;

function CreateMesh({
  useGame,
  usePet,
  petName,
  topIconName,
}: {
  usePet: string;
  useGame: any;
  petName: string;
  topIconName: string;
}) {
  const groupRef = useRef<THREE.Group>(null);
  const curAnimation = useGame((state: UseGameState) => state.curAnimation);
  const worldPosRef = useRef(new THREE.Vector3());
  const worldQuaternionRef = useRef(new THREE.Quaternion());
  const [petEntity, setPetEntity] = useState<Entity | null>(null);

  useEffect(() => {
    const entity = EntityManager.getInstance().createClientEntity([
      ComponentType.Transform,
      ComponentType.Animation,
      ComponentType.GlbMesh,
      ComponentType.TopText,
    ]);
    index++;
    entity.name = petName ?? `pet_${index}`;
    // entity.name = `pet_${index}`;
    const topText = entity.getComponent<EntityTopText>(ComponentType.TopText);
    topText?.setTopIconName(topIconName);
    topText?.setText(entity.name);
    const animation = entity.getComponent<EntityAnimation>(ComponentType.Animation);
    animation?.setDefaultAnimation('Action_00');
    animation?.setReplaceAnimations(['Action_00', 'Action_01', 'Action_03']);
    setPetEntity(entity);
    return () => {
      EntityManager.getInstance().removeEntity(entity);
    };
  }, []);

  useEffect(() => {
    if (petEntity && petEntity.name !== petName) {
      const topText = petEntity.getComponent<EntityTopText>(ComponentType.TopText);
      petEntity.name = petName ?? `pet_${index}`;
      topText?.setText(petEntity.name);
      setPetEntity(petEntity);
    }
  }, [petName, petEntity, topIconName]);

  useEffect(() => {
    if (!petEntity) return;
    const animation = petEntity.getComponent<EntityAnimation>(ComponentType.Animation);
    animation?.playAnimation(curAnimation || 'idle');
  }, [curAnimation, petEntity]);

  useEffect(() => {
    if (!petEntity) return;
    const glbMesh = petEntity.getComponent<EntityGlbMesh>(ComponentType.GlbMesh);
    glbMesh?.setGlbUrl(usePet);
  }, [usePet, petEntity]);

  useFrame(() => {
    const group = groupRef.current;
    if (!group || !petEntity) return;
    group.getWorldPosition(worldPosRef.current);
    group.getWorldQuaternion(worldQuaternionRef.current);
    const transform = petEntity.getComponent<EntityTransform>(ComponentType.Transform);
    // console.log('worldPosRef.current', transform, worldPosRef.current);
    transform?.setPosition(worldPosRef.current, worldQuaternionRef.current);
  });
  return <group ref={groupRef} />;
}

function PetModel({ petFollowData }: { petFollowData: FollowPetItem }) {
  const { petTag, curPosition, targetPosition, petName, serverId, topIconName } = petFollowData;
  const groupRef = useRef<THREE.Group>(null);
  const useGame = useMemo(() => createUseGame(), []);
  const setWeary = useGame((state: UseGameState) => state.setWeary);
  const petList = useAppSelector((state) => state.GameReducer.petList);

  const initializeAnimationSet = useGame((state: UseGameState) => state.initializeAnimationSet);
  const idle = useGame((state) => state.idle);
  const setCurAnimation = useGame((state) => state.setCurAnimation);
  const setMoveToPoint = useGame((state) => state.setMoveToPoint);
  const setTransformPoint = useGame((state) => state.setTransformPoint);
  const [usePet, setUsePet] = useState<string>('');

  const getRandomOffset = () => {
    const offsetList = [
      new THREE.Vector3(1, 0.5, 0),
      new THREE.Vector3(-1, 0.5, 0),
      new THREE.Vector3(0, 0.5, 1),
      new THREE.Vector3(0, 0.5, -1),
    ];
    return offsetList[Math.floor(Math.random() * offsetList.length)];
  };

  useEffect(() => {
    let timer: NodeJS.Timeout | null = null;

    const petInfo = petList.find((item) => item._id === serverId);
    if (petInfo) {
      const now = Date.now();
      const freeTime = petInfo.freeTime;

      const isWeary = freeTime > 0 && now < freeTime;
      setWeary(isWeary);

      if (isWeary) {
        timer = setTimeout(() => {
          setWeary(false);
        }, freeTime - now);
      }
    }

    return () => {
      if (timer) clearTimeout(timer);
    };
  }, [serverId, petList]);

  useEffect(() => {
    initializeAnimationSet({
      idle: 'Action_00',
      walk: 'Action_03',
      run: 'Action_01',
      jump: 'jump_01',
      jumpIdle: 'jump_02',
      jumpLand: 'jump_03',
      fall: 'fall',
    });
  }, [initializeAnimationSet]);

  const latestUsePet = useLatest(usePet);
  const transformCd = useRef(0);
  const lockMove = useRef(false);
  useEffect(() => {
    let cancel = false;
    const targetFinish = (callback: () => void) => {
      if (cancel) return;
      if (
        !targetPosition ||
        isNaN(targetPosition.x) ||
        isNaN(targetPosition.y) ||
        isNaN(targetPosition.z)
      ) {
        setTimeout(() => {
          targetFinish(callback);
        }, 500);
        return;
      }
      callback();
    };
    PetConfig.getInstance().getData(petTag, (data) => {
      if (cancel) return;
      if (latestUsePet.current === data.glbUrl) {
        return;
      } else {
        setUsePet('');
      }
      targetFinish(() => {
        setUsePet(data.glbUrl);
      });
    });
    return () => {
      cancel = true;
    };
  }, [petTag, targetPosition]);

  useFrame(() => {
    // console.log('curPosition', curPosition, rigidRef.current);
    if (!groupRef.current) return;
    // console.log('targetPosition', targetPosition);
    const petPos = groupRef.current.getWorldPosition(curPosition);
    if (!targetPosition) return;
    const distance = targetPosition.distanceTo(petPos);
    const osTime = Date.now();
    if (transformCd.current < osTime && distance > 8) {
      const randomOffset = getRandomOffset();
      randomOffset.add(targetPosition);
      transformCd.current = osTime + 3000;
      setTransformPoint(randomOffset);
      // GlobalSpaceEvent.SetDataValue<TransformData>(GlobalDataKey.TransformData, {
      //   characterType: CharacterType.Pet,
      //   position: randomOffset,
      // });
      setMoveToPoint(null);
      if (idle) {
        idle();
      }
      if (setCurAnimation) {
        setCurAnimation('Action_1001');
        lockMove.current = true;
        setTimeout(() => {
          lockMove.current = false;
        }, 2000);
      }
    } else {
      if (lockMove.current) return;
      setMoveToPoint(targetPosition);
    }
  });

  return (
    <>
      {usePet.length > 0 && (
        <Ecctrl
          mode="PointToMove"
          useGame={useGame}
          animated={true}
          disableFollowCam={true}
          position={[1, 1, 0]}>
          <group position={[0, -0.92, 0]} ref={groupRef}>
            <CreateMesh
              useGame={useGame}
              usePet={usePet}
              petName={petName}
              topIconName={topIconName}
            />
          </group>
        </Ecctrl>
      )}
    </>
  );
}

export default PetModel;
