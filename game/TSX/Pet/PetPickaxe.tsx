/*
Auto-generated by: https://github.com/pmndrs/gltfjsx
*/
import * as THREE from 'three';
import { Entity } from '@/game/TS/Entity/Entity';
import { useCallback, useEffect, useState } from 'react';
import { ComponentType } from '@/game/TS/Entity/Enum';
import { EntityWalkPoint } from '@/game/TS/Entity/Components/EntityWalkPoint';
import { EntityAnimation } from '@/game/TS/Entity/Components/EntityAnimation';
import { AnimationType } from '@/game/TSX/Pet/PetEntity';
import { UseGameState } from '@/game/Lib/stores/useGame';
import { AudioSystem } from '@/game/Global/GlobalAudioSystem';
import usePetWork from '@/hooks/usePetWork';
import { StoneConfig, StoneData } from '@/game/Config/StoneConfig';
import { PetFeaturesData } from '@/game/Config/PetFeaturesConfig';
import { getParticleSystem } from '@/game/TSX/Particles/ParticleSystem';
import { TreeConfig } from '@/game/Config/TreeConfig';

enum MiningStatus {
  Idle,
  MoveToStone,
  HitStone,
}

function FindCloseStone({
  entity,
  findSuccess,
}: {
  entity: Entity;
  findSuccess: (stoneData: StoneData) => void;
}) {
  const findAliveStone = useCallback(
    (animation: EntityAnimation, position: THREE.Vector3) => {
      const stoneDataMap = StoneConfig.getInstance().getStoneDataMap();
      const aliveObjectList: { data: StoneData; id: number; distance: number }[] = [];
      stoneDataMap.forEach((item) => {
        const stoneData = item.data;
        const object = StoneConfig.getInstance().getObject(stoneData.id);
        if (object.status === 'dead') {
          return;
        }
        if (object.petUse) {
          return;
        }
        const distance = position.distanceTo(
          new THREE.Vector3(stoneData.position[0], position.y, stoneData.position[2])
        );
        aliveObjectList.push({ id: stoneData.id, data: stoneData, distance: distance });
      });
      aliveObjectList.sort((a, b) => a.distance - b.distance);
      if (aliveObjectList.length > 0) {
        animation.playAnimation(AnimationType.Idle);
        findSuccess(aliveObjectList[0].data);
        return;
      }
      animation.playAnimation(AnimationType.Idle);
    },
    [findSuccess]
  );

  useEffect(() => {
    const walkPoint = entity.getComponent<EntityWalkPoint>(ComponentType.WalkPoint);
    const animation = entity.getComponent<EntityAnimation>(ComponentType.Animation);
    if (!animation || !walkPoint) return;
    walkPoint.position && findAliveStone(animation, walkPoint.position);
    const interval = setInterval(() => {
      walkPoint.position && findAliveStone(animation, walkPoint.position);
    }, 3000);
    return () => {
      clearInterval(interval);
    };
  }, [entity, findAliveStone]);
  return null;
}

function MoveToStone({
  entity,
  stoneData,
  moveToStoneEnd,
}: {
  entity: Entity;
  stoneData: StoneData;
  moveToStoneEnd: () => void;
}) {
  const [targetPosition, setTargetPosition] = useState<THREE.Vector3 | null>(null);
  useEffect(() => {
    const centerPoint = new THREE.Vector3(-155, stoneData.position[1], 36);
    const targetPoint = new THREE.Vector3(
      stoneData.position[0],
      stoneData.position[1],
      stoneData.position[2]
    );
    const distance = centerPoint.distanceTo(targetPoint);
    const normal = targetPoint.clone().sub(centerPoint).normalize();
    // const radius = (stoneData.range[1] + stoneData.range[2]) / 2 - 0.6;
    const radius = stoneData.range[1] - 0.6;
    const target = centerPoint.clone().add(normal.multiplyScalar(distance - radius));
    setTargetPosition(target);
  }, [entity, stoneData]);

  useEffect(() => {
    const walkPoint = entity.getComponent<EntityWalkPoint>(ComponentType.WalkPoint);
    const animation = entity.getComponent<EntityAnimation>(ComponentType.Animation);
    if (!walkPoint || !animation || !targetPosition) return;
    animation.playAnimation(AnimationType.Walk);
    walkPoint.setTarget(targetPosition);
    walkPoint.setArriveCallback((isTransform: boolean) => {
      animation.playAnimation(AnimationType.Idle);
      moveToStoneEnd();
    });
  }, [entity, moveToStoneEnd, targetPosition]);
  return null;
}

function HitStone({
  features,
  serverId,
  entity,
  stoneData,
  hitStoneEnd,
}: {
  features: PetFeaturesData;
  serverId: string;
  entity: Entity;
  stoneData: StoneData;
  hitStoneEnd: () => void;
}) {
  const particleSystem = getParticleSystem();
  const { handleSocketPetPickaxe } = usePetWork();
  const stoneObject = StoneConfig.getInstance().getObject(stoneData.id);
  const useGame = stoneObject.useGame;
  const setStoneCurAnimation = useGame((state: UseGameState) => state.setCurAnimation);
  const damageStone = useCallback(
    (damage: number, position: THREE.Vector3, quaternion: THREE.Quaternion) => {
      if (stoneObject.hp <= 0) {
        hitStoneEnd();
        //放置重复请求
        return;
      }
      stoneObject.hp -= damage;
      particleSystem.addParticle(
        position,
        quaternion,
        './particles/Effect_stone_0.json',
        stoneData.hit_effect_scale,
        stoneData.hit_effect_during
      );
      particleSystem.addParticle(
        position,
        quaternion,
        './particles/Effect_stone_1.json',
        stoneData.hit_effect_scale,
        stoneData.hit_effect_during
      );
      if (stoneObject.hp > 0) {
        setStoneCurAnimation('shake');
      } else {
        setStoneCurAnimation('fall');
        setTimeout(() => {
          stoneObject.status = 'dead';
          handleSocketPetPickaxe(stoneObject.tag, stoneObject.id, serverId);
          hitStoneEnd();
        }, stoneData.disappear_time);
        AudioSystem.playAudio(
          'scene_stone_' + stoneData.id,
          StoneConfig.getInstance().getFallSound(stoneData),
          () => {
            return true;
          }
        );
      }
    },
    [stoneObject, hitStoneEnd, setStoneCurAnimation, stoneData, handleSocketPetPickaxe, serverId]
  );

  useEffect(() => {
    const animation = entity.getComponent<EntityAnimation>(ComponentType.Animation);
    const walkPoint = entity.getComponent<EntityWalkPoint>(ComponentType.WalkPoint);
    if (!animation || !walkPoint) return;
    let cancel = false;
    const attack = () => {
      walkPoint.setFacePosition(
        new THREE.Vector3(stoneData.position[0], stoneData.position[1], stoneData.position[2])
      );
      animation.playAnimation(AnimationType.Action);
      setTimeout(() => {
        if (cancel || !walkPoint || !walkPoint.position) return;
        const stonePos = new THREE.Vector3(
          stoneData.position[0],
          stoneData.position[1] + 0.5,
          stoneData.position[2]
        );
        const object = new THREE.Object3D();
        object.position.copy(walkPoint.position);
        object.lookAt(stonePos);
        const object2 = new THREE.Object3D();
        object2.position.set(0, 0.18, 1.5);
        object.add(object2);
        const hitPos = new THREE.Vector3(0, 0, 0);
        object2.getWorldPosition(hitPos);
        // hitPos.y = treePos.y
        damageStone(features.damage, hitPos, object.quaternion);
        animation.playAnimation(AnimationType.Idle);
      }, 1000);
    };
    attack();
    const interval = setInterval(() => {
      attack();
    }, features.interval);
    return () => {
      cancel = true;
      clearInterval(interval);
    };
  }, [entity, damageStone, stoneData, features]);

  return null;
}

function PetPickaxe({
  serverId,
  petEntity,
  features,
}: {
  serverId: string;
  petEntity: Entity;
  features: PetFeaturesData;
}) {
  const [status, setStatus] = useState(MiningStatus.Idle);
  const [targetStoneData, setTargetStoneData] = useState<StoneData | null>(null);

  useEffect(() => {
    if (!targetStoneData) return;
    const stoneObject = StoneConfig.getInstance().getObject(targetStoneData.id);
    stoneObject.petUse = true;
    return () => {
      stoneObject.petUse = false;
    };
  }, [targetStoneData]);

  const findCloseStoneSuccess = (stoneData: StoneData) => {
    setTargetStoneData(stoneData);
    setStatus(MiningStatus.MoveToStone);
  };

  const moveToStoneEnd = () => {
    setStatus(MiningStatus.HitStone);
  };

  const hitStoneEnd = () => {
    setStatus(MiningStatus.Idle);
  };

  return (
    <>
      {status === MiningStatus.Idle && (
        <FindCloseStone entity={petEntity} findSuccess={findCloseStoneSuccess} />
      )}
      {status === MiningStatus.MoveToStone && targetStoneData && (
        <MoveToStone
          entity={petEntity}
          stoneData={targetStoneData}
          moveToStoneEnd={moveToStoneEnd}
        />
      )}
      {status === MiningStatus.HitStone && targetStoneData && (
        <HitStone
          features={features}
          serverId={serverId}
          entity={petEntity}
          stoneData={targetStoneData}
          hitStoneEnd={hitStoneEnd}
        />
      )}
    </>
  );
}

export default PetPickaxe;
