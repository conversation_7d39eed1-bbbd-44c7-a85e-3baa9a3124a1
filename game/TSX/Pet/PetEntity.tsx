/*
Auto-generated by: https://github.com/pmndrs/gltfjsx
*/

import * as THREE from 'three';
import { useEffect, useMemo, useState } from 'react';
import { PetConfig, PetData } from '@/game/Config/PetConfig';
import { Entity } from '@/game/TS/Entity/Entity';
import { EntityManager } from '@/game/TS/Entity/EntityManager';
import { ComponentType } from '@/game/TS/Entity/Enum';
import { EntityTopText } from '@/game/TS/Entity/Components/EntityTopText';
import { EntityAnimation } from '@/game/TS/Entity/Components/EntityAnimation';
import { EntityGlbMesh } from '@/game/TS/Entity/Components/EntityGlbMesh';
import { EntityWalkPoint } from '@/game/TS/Entity/Components/EntityWalkPoint';
import { getParticleSystem } from '@/game/TSX/Particles/ParticleSystem';
import { useAppSelector } from '@/hooks/useStore';
import { IGameState } from '@/constant/type';
import { PetStatus, Rarity } from '@/constant/enum';
import { GetMyPlayer } from '@/game/TSX/Character/MyPlayer';
import PetStandby from '@/game/TSX/Pet/PetStandby';
import PetFollow from '@/game/TSX/Pet/PetFollow';
import PetAxe from '@/game/TSX/Pet/PetAxe';
import PetPickaxe from '@/game/TSX/Pet/PetPickaxe';
import { PetFeaturesConfig, PetFeaturesData, PetFeaturesType } from '@/game/Config/PetFeaturesConfig';
import PetFishingPole from '@/game/TSX/Pet/PetFishingPole';
import { EntityFollowBone } from '@/game/TS/Entity/Components/EntityFollowBone';

export enum AnimationType {
  Standby = 'standby',
  Walk = 'walk',
  Idle = 'idle',
  Action = 'action',
  EnterWater = 'enterWater',
  LeaveWater = 'leaveWater',
}

export interface PetFollowData {
  curPosition: THREE.Vector3;
  followSlot: number;
  followPositions: THREE.Vector3[];
}

function PetEntity({
  serverId,
  petTag,
  petName,
  petFollowData,
  topIconName,
}: {
  serverId: string;
  petTag: string;
  petName: string;
  petFollowData: PetFollowData;
  topIconName: string;
}) {
  const { petList } = useAppSelector((state: { GameReducer: IGameState }) => state.GameReducer);
  const [axeFeatures, setAxeFeatures] = useState<PetFeaturesData | null>(null);
  const [pickaxeFeatures, setPickaxeFeatures] = useState<PetFeaturesData | null>(null);
  const [fishingPoleFeatures, setFishingPoleFeatures] = useState<PetFeaturesData | null>(null);
  const petInfo = useMemo(() => {
    return petList.find((item) => item._id === serverId);
  }, [petList, serverId]);
  const petStatus = useMemo(() => {
    return petInfo?.petStatus || PetStatus.REST;
    // PetFeaturesConfig.getInstance().getData(PetFeaturesType.FishingPole, 1, (data) => {
    //   setFishingPoleFeatures(data);
    // });
    // return PetStatus.FISHING_POLE;
  }, [petInfo]);
  const bindingFlag = useMemo(() => {
    return petInfo?.bindingFlag ?? true;
  }, [petInfo]);
  const petQuality = useMemo(() => {
    return petInfo?.quality || Rarity.COMMON;
  }, [petInfo]);
  const particleSystem = getParticleSystem();
  const [petEntity, setPetEntity] = useState<Entity | null>(null);
  const [haloEntity, setHaloEntity] = useState<Entity | null>(null);

  const [weary, setWeary] = useState(false);
  const [petData, setPetData] = useState<PetData | null>(null);

  const getRandomOffset = (oldPosition: THREE.Vector3) => {
    const offsetList = [
      new THREE.Vector3(0.5, 0, 0),
      new THREE.Vector3(-0.5, 0, 0),
      new THREE.Vector3(0, 0, 0.5),
      new THREE.Vector3(0, 0, -0.5),
    ];
    const offset = offsetList[Math.floor(Math.random() * offsetList.length)];
    return oldPosition.clone().add(offset);
  };

  useEffect(() => {
    let cancel = false;
    PetConfig.getInstance().getData(petTag, (data) => {
      if (cancel) return;
      setPetData(data);
    });
    return () => {
      setPetData(null);
      cancel = true;
    };
  }, [petTag]);

  useEffect(() => {
    if (!petEntity) return;
    petEntity.name = petName;
    const topText = petEntity.getComponent<EntityTopText>(ComponentType.TopText);
    topText?.setText(petEntity.name, PetConfig.getInstance().getNameColor(petQuality));
    topText?.setTopIconName(topIconName);

    if (bindingFlag) return;
    const haloEntity = EntityManager.getInstance().createClientEntity([
      ComponentType.FollowBone,
      ComponentType.Animation,
      ComponentType.GlbMesh,
    ]);
    haloEntity.name = 'halo';
    const haloMesh = haloEntity.getComponent<EntityGlbMesh>(ComponentType.GlbMesh);
    haloMesh?.setGlbUrl(PetConfig.getInstance().getHaloMeshLink(petQuality));
    haloMesh?.setShadow(false, 'light');
    const followBone = haloEntity.getComponent<EntityFollowBone>(ComponentType.FollowBone);
    followBone?.setTarget(petEntity.clientIndex, 'transform');
    const animation = haloEntity.getComponent<EntityAnimation>(ComponentType.Animation);
    animation?.playAnimation('Action_00');
    animation?.setReplaceAnimations(['Action_00']);
    setHaloEntity(haloEntity);
    return () => {
      setHaloEntity(null);
      EntityManager.getInstance().removeEntity(haloEntity);
    };
  }, [petQuality, bindingFlag, petEntity, topIconName, petName]);

  useEffect(() => {
    petInfo?.featureInfos.forEach((item) => {
      switch (item.feature) {
        case PetFeaturesType.FishingPole:
          PetFeaturesConfig.getInstance().getData(item.feature, item.featureLevel, (data) => {
            setFishingPoleFeatures(data);
          });
          break;
        case PetFeaturesType.Axe:
          PetFeaturesConfig.getInstance().getData(item.feature, item.featureLevel, (data) => {
            setAxeFeatures(data);
          });
          break;
        case PetFeaturesType.Pickaxe:
          PetFeaturesConfig.getInstance().getData(item.feature, item.featureLevel, (data) => {
            setPickaxeFeatures(data);
          });
          break;
      }
    });
    let timer: NodeJS.Timeout | null = null;
    if (petInfo) {
      const now = Date.now();
      const freeTime = petInfo.freeTime;

      const isWeary = freeTime > 0 && now < freeTime;
      setWeary(isWeary);

      if (isWeary) {
        timer = setTimeout(() => {
          setWeary(false);
        }, freeTime - now);
      }
    }

    return () => {
      if (timer) clearTimeout(timer);
    };
  }, [serverId, petInfo]);

  useEffect(() => {
    if (!petData) return;
    const entity = EntityManager.getInstance().createClientEntity([
      ComponentType.WalkPoint,
      ComponentType.Animation,
      ComponentType.GlbMesh,
      ComponentType.TopText,
    ]);
    const myPlayer = GetMyPlayer();
    const position = getRandomOffset(myPlayer.position);
    const walkPoint = entity.getComponent<EntityWalkPoint>(ComponentType.WalkPoint);
    walkPoint?.setTarget(position);
    const animation = entity.getComponent<EntityAnimation>(ComponentType.Animation);
    animation?.setDefaultAnimation('Action_00');
    animation?.setReplaceAnimations(['Action_00']);
    const glbMesh = entity.getComponent<EntityGlbMesh>(ComponentType.GlbMesh);
    glbMesh?.setGlbUrl(petData.glbUrl);
    setPetEntity(entity);
    particleSystem.addParticle(
      position,
      new THREE.Quaternion(),
      './particles/Effect_pet_call01.json',
      1,
      3000
    );
    particleSystem.addParticle(
      position,
      new THREE.Quaternion(),
      './particles/Effect_pet_call02.json',
      1,
      3000
    );
    return () => {
      const walkPoint = entity.getComponent<EntityWalkPoint>(ComponentType.WalkPoint);
      if (walkPoint) {
        particleSystem.addParticle(
          walkPoint.point,
          new THREE.Quaternion(),
          './particles/Effect_pet_recall.json',
          1,
          3000
        );
      }
      EntityManager.getInstance().removeEntity(entity);
    };
  }, [particleSystem, petData]);

  useEffect(() => {
    if (!petEntity) return;
    const animation = petEntity.getComponent<EntityAnimation>(ComponentType.Animation);
    animation?.setAnimationRename(AnimationType.Standby, 'Action_04');
    animation?.setAnimationRename(AnimationType.Walk, weary ? 'Action_03' : 'Action_01');
    animation?.setAnimationRename(AnimationType.Idle, weary ? 'Action_02' : 'Action_00');
    animation?.setAnimationRename(AnimationType.Action, 'Action_05');
    animation?.setAnimationRename(AnimationType.EnterWater, 'Action_06');
    animation?.setAnimationRename(AnimationType.LeaveWater, 'Action_07');

    animation?.setReplaceAnimations([
      AnimationType.Standby,
      AnimationType.Walk,
      AnimationType.Idle,
    ]);
  }, [petEntity, weary]);

  return (
    <>
      {petEntity && petStatus == PetStatus.FOLLOW && (
        <PetFollow petEntity={petEntity} followData={petFollowData} />
      )}
      {petEntity && petStatus == PetStatus.IDLE && <PetStandby petEntity={petEntity} />}
      {petEntity && petStatus == PetStatus.AXE && axeFeatures && (
        <PetAxe serverId={serverId} petEntity={petEntity} features={axeFeatures} />
      )}
      {petEntity && petStatus == PetStatus.PICKAXE && pickaxeFeatures && (
        <PetPickaxe serverId={serverId} petEntity={petEntity} features={pickaxeFeatures} />
      )}
      {petEntity && petStatus == PetStatus.FISHING_POLE && fishingPoleFeatures && (
        <PetFishingPole
          serverId={serverId}
          petEntity={petEntity}
          haloEntity={haloEntity}
          features={fishingPoleFeatures}
        />
      )}
    </>
  );
}

export default PetEntity;
