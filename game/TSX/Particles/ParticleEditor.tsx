import * as THREE from 'three';
import { useEffect, useRef, useState } from 'react';
import { AppGameApiKey, GetMyPlayer } from '@/game/TSX/Character/MyPlayer';
import ParticleEditObject from '@/game/TSX/Particles/ParticleEditObject';

export interface ParticleEditJson {
  position: number[];
  mapUrl: string;
  minEmitNumber: number;
  maxEmitNumber: number;
  minEmitTime: number;
  maxEmitTime: number;
  minRadius: number;
  maxRadius: number;
  minLife: number;
  maxLife: number;
  minMass: number;
  maxMass: number;
  initPositionSet: any[];
  velocity: {
    dir: number[];
    minSpeed: number;
    maxSpeed: number;
    tha: number;
  };
  gravity: number;
  alphaStart: number;
  alphaEnd: number;
  scaleStart: number;
  scaleEnd: number;
  startColor: string;
  endColor: string;
  isStartColorRandom: boolean;
  isEndColorRandom: boolean;
  enableEndColor: boolean;
  drift: {
    force: number[];
    interval: number;
  };
}

export interface ParticleEditData {
  uuid: string;
  position: THREE.Vector3;
  config: ParticleEditJson | null;
  isEdit: boolean;
}

export default function ParticleEditor() {
  const groupRef = useRef<THREE.Group>(null);
  const [particleList, setParticleList] = useState<ParticleEditData[]>([]);
  const myPlayer = GetMyPlayer();
  useEffect(() => {
    myPlayer.setAppApi(AppGameApiKey.createParticle, (position: THREE.Vector3) => {
      const newParticle = {
        uuid: THREE.MathUtils.generateUUID(),
        position: position.clone(),
        config: null,
        isEdit: true,
      };
      setParticleList([...particleList, newParticle]);
    });
  }, [particleList]);

  return (
    <group ref={groupRef}>
      {particleList.length > 0 &&
        particleList.map((particleData) => <ParticleEditObject particleData={particleData} />)}
    </group>
  );
}
