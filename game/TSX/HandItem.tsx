import * as THREE from 'three';
import { useEffect, useState } from 'react';
import { useFrame } from '@react-three/fiber';
import { ItemConfig, ItemData, ItemType } from '@/game/Config/ItemConfig';
import { GetMyPlayer } from '@/game/TSX/Character/MyPlayer';
import { UseGameState } from '@/game/Lib/stores/useGame';
import { getParticleSystem } from '@/game/TSX/Particles/ParticleSystem';
import { AudioSystem } from '@/game/Global/GlobalAudioSystem';
import { KeyPressUtil } from '@/game/Global/GlobalKeyPressUtil';
import { Entity } from '@/game/TS/Entity/Entity';
import { EntityManager } from '@/game/TS/Entity/EntityManager';
import { ComponentType } from '@/game/TS/Entity/Enum';
import { EntityFollowBone } from '@/game/TS/Entity/Components/EntityFollowBone';
import { EntityAnimation } from '@/game/TS/Entity/Components/EntityAnimation';
import { EntityGlbMesh } from '@/game/TS/Entity/Components/EntityGlbMesh';
import { EntityEffect } from '@/game/TS/Entity/Components/EntityEffect';

let myHandItemServerId = '';
let myHandItemId = 0;
let timerList: any[] = [];

function changeHandItemId(
  setCurAnimation: any,
  myPlayer: any,
  serverItemId: string,
  itemId: number
) {
  if (serverItemId === myHandItemServerId && itemId === myHandItemId) {
    return;
  }
  myHandItemServerId = serverItemId;
  myHandItemId = itemId;

  timerList.forEach((timer) => {
    clearTimeout(timer);
  });
  timerList = [];

  if (itemId > 0) {
    if (myPlayer.isNewServerItemId(serverItemId)) {
      myPlayer.faceCamera();
      myPlayer.saveServerItemId(serverItemId);
      setCurAnimation('Action_11');
      timerList.push(
        setTimeout(() => {
          AudioSystem.playAudio('myPlayer', './sound/reward/reward_axe.mp3', () => {
            return true;
          });
        }, 650)
      );
      KeyPressUtil.setEnable(false);
      timerList.push(
        setTimeout(() => {
          KeyPressUtil.setEnable(true);
        }, 2500)
      );
    } else {
      KeyPressUtil.setEnable(true);
    }
  } else {
    if (serverItemId.length > 0) {
      myPlayer.faceCamera();
      const showDisappearEffect = () => {
        const particleSystem = getParticleSystem();
        const tempObject = new THREE.Object3D();
        tempObject.position.copy(myPlayer.position);
        tempObject.quaternion.copy(myPlayer.quaternion);
        const tempObject2 = new THREE.Object3D();
        tempObject2.position.set(0, 0.6, 0.5);
        tempObject.add(tempObject2);

        const effectPos = tempObject2.getWorldPosition(new THREE.Vector3());
        particleSystem.addParticle(
          effectPos,
          new THREE.Quaternion(),
          './particles/Effect_broken.json',
          0.3,
          3000
        );
      };

      setCurAnimation('Action_10');
      timerList.push(
        setTimeout(() => {
          showDisappearEffect();
        }, 450)
      );
      timerList.push(
        setTimeout(() => {
          AudioSystem.playAudio('myPlayer', './sound/break/axe_damage.mp3', () => {
            return true;
          });
        }, 400)
      );
      KeyPressUtil.setEnable(false);
      timerList.push(
        setTimeout(() => {
          myPlayer.destroyHandItem();
          KeyPressUtil.setEnable(true);
        }, 2500)
      );
    } else {
      KeyPressUtil.setEnable(true);
    }
  }
}

export interface HandItemDetail {
  itemId: number;
  isMe: boolean;
  curDurability: number;
  showEffect: boolean;
  serverId: string;
  useGame: any;
}

export default function HandItem({
  targetClientIndex,
  itemDetail,
  useGame,
}: {
  targetClientIndex: number;
  useGame: any;
  itemDetail: HandItemDetail;
}) {
  const myPlayer = GetMyPlayer();
  const initializeAnimationSet = useGame((state: UseGameState) => state.initializeAnimationSet);
  const setCurAnimation = useGame((state: UseGameState) => state.setCurAnimation);
  const playerAction = useGame((state: UseGameState) => state.curAnimation);
  const [itemId, setItemId] = useState<number>(0);
  const [serverId, setServerId] = useState<string>('');
  const [itemData, setItemData] = useState<ItemData | null>(null);

  const [handItemEntity, setHandItemEntity] = useState<Entity | null>(null);

  useEffect(() => {
    ItemConfig.getInstance().getData(itemId, (data) => {
      initializeAnimationSet(
        {
          idle: data && data.action_idle.length > 0 ? data.action_idle : 'idle',
          wearyIdle:
            data && data.action_weary_idle.length > 0 ? data.action_weary_idle : 'weary_idle',
          walk: data && data.action_walk.length > 0 ? data.action_walk : 'walk',
          wearyWalk:
            data && data.action_weary_walk.length > 0 ? data.action_weary_walk : 'weary_walk',
          run: data && data.action_run.length > 0 ? data.action_run : 'run',
          jump: 'jump_01',
          jumpIdle: 'jump_02',
          jumpLand: 'jump_03',
          fall: 'fall', // This is for falling from high sky
        },
        true
      );
      if (itemDetail.isMe) {
        myPlayer.changeHandItem(data);
        changeHandItemId(setCurAnimation, myPlayer, serverId, itemId);
      }
      setItemData(data);
    });
  }, [itemId, serverId]);

  useEffect(() => {
    if (!handItemEntity) {
      return;
    }
    const animationComponent = handItemEntity.getComponent<EntityAnimation>(
      ComponentType.Animation
    );
    const glbMeshComponent = handItemEntity.getComponent<EntityGlbMesh>(ComponentType.GlbMesh);
    if (animationComponent && glbMeshComponent) {
      switch (playerAction) {
        case 'Action_16': // 甩干
          animationComponent.playAnimation('Action_fishing_00');
          glbMeshComponent.setVisible(true, 'Floats');
          setTimeout(() => {
            glbMeshComponent.setVisible(false, 'Floats');
          }, 1300);
          break;
        case 'Action_17': // 等待
          animationComponent.playAnimation('Action_fishing_01');
          break;
        case 'Action_18': // 挣扎
          animationComponent.playAnimation('Action_fishing_02');
          break;
        case 'Action_20': // 收杆
          animationComponent.playAnimation('Action_fishing_03');
          glbMeshComponent.setVisible(true, 'Floats');
          setTimeout(() => {
            glbMeshComponent.setVisible(false, 'Floats');
          }, 600);
          break;
        default:
          animationComponent.playAnimation('Action_fishing_04');
      }
    }
  }, [handItemEntity, playerAction]);

  useEffect(() => {
    if (itemData) {
      const entity = EntityManager.getInstance().createClientEntity([
        ComponentType.FollowBone,
        ComponentType.Animation,
        ComponentType.GlbMesh,
        ComponentType.Effect,
      ]);
      entity.name = 'HandItem';
      const glbMeshComponent = entity.getComponent<EntityGlbMesh>(ComponentType.GlbMesh);
      if (glbMeshComponent) {
        glbMeshComponent.setGlbUrl(itemData.glb_url);
        glbMeshComponent.setVisible(false, 'Floats');
      }
      const followBone = entity.getComponent<EntityFollowBone>(ComponentType.FollowBone);
      if (followBone) {
        followBone.setTarget(targetClientIndex, 'Bip001_R_Hand');
        followBone.setPosition(
          new THREE.Vector3(itemData.position[0], itemData.position[1], itemData.position[2]),
          new THREE.Quaternion().setFromEuler(
            new THREE.Euler(
              (itemData.rotation[0] * Math.PI) / 180,
              (itemData.rotation[1] * Math.PI) / 180,
              (itemData.rotation[2] * Math.PI) / 180
            )
          )
        );
      }
      const effectComponent = entity.getComponent<EntityEffect>(ComponentType.Effect);
      if (effectComponent) {
        effectComponent.setEffect(
          itemData.effect_url ? itemData.effect_url : '',
          itemData.effect_scale ? itemData.effect_scale : 1
        );
      }
      if (itemData.type === ItemType.FishingRod) {
        const animationComponent = entity.getComponent<EntityAnimation>(ComponentType.Animation);
        if (animationComponent) {
          animationComponent.setDefaultAnimation('Action_fishing_01');
          animationComponent.setReplaceAnimations([
            'Action_fishing_01',
            'Action_fishing_02',
            'Action_fishing_04',
          ]);
        }
      }
      setHandItemEntity(entity);
      return () => {
        setHandItemEntity(null);
        EntityManager.getInstance().removeEntity(entity);
      };
    }
  }, [targetClientIndex, itemData]);

  useFrame(() => {
    if (itemDetail.curDurability > 0) {
      setItemId(itemDetail.itemId);
      setServerId(itemDetail.serverId);
    } else {
      setServerId(itemDetail.serverId);
      setItemId(0);
    }

    // setShowEffect(itemDetail.showEffect)
  });

  return null;
}
