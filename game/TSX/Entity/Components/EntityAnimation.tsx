import * as THREE from 'three';
import React, { useEffect, useMemo, useState } from 'react';
import { useAnimations } from '@react-three/drei';
import { EntityAnimation } from '@/game/TS/Entity/Components/EntityAnimation';
import EntityFaceTSX from '@/game/TSX/Entity/Components/EntityFace';
import { EntityFace } from '@/game/TS/Entity/Components/EntityFace';
import { AvatarActionConfig } from '@/game/Config/AvatarActionConfig';
import AudioSystemComponent, { AudioSystem } from '@/game/Global/GlobalAudioSystem';

function Animation({
  animationsRef,
  animation,
  root,
  curAnimation,
  audioKey,
}: {
  audioKey: string;
  curAnimation: string;
  root: THREE.Object3D;
  animation: EntityAnimation;
  animationsRef: THREE.AnimationClip[];
}) {
  const [animations, setAnimations] = useState<THREE.AnimationClip[]>([...animationsRef]);
  const { actions } = useAnimations(animations, root);

  useEffect(() => {
    setAnimations([...animationsRef]);
  }, [animationsRef]);

  useEffect(() => {
    if (animationsRef.length !== animations.length) {
      setAnimations([...animationsRef]);
      return;
    }
    const finishCall = () => {
      animation.playAnimation(animation.defaultAnimation);
    };

    const actionList: string[] = curAnimation.split('|');

    const actionKey = actionList[0];
    const actionSpeed = Number(actionList[1] || 1);

    // Play animation
    const action: THREE.AnimationAction | null = actions[actionKey || ''];

    if (action) {
      action.timeScale = actionSpeed;
      // For jump and jump land animation, only play once and clamp when finish
      if (!animation.replaceAnimations.includes(actionKey)) {
        action.reset().fadeIn(0.2).setLoop(THREE.LoopOnce, 0).play();
        action.clampWhenFinished = true;
      } else {
        action.reset().fadeIn(0.2).play();
      }
      // When any action is clamp and finished reset animation
      (action as any)._mixer.addEventListener('finished', () => finishCall());
    }
    return () => {
      if (action) {
        // Fade out previous action
        action.fadeOut(0.2);
        // Clean up mixer listener, and empty the _listeners array
        (action as any)._mixer.removeEventListener('finished', () => finishCall());
        (action as any)._mixer._listeners = [];
      }
    };
  }, [curAnimation, animations]);

  useEffect(() => {
    if (curAnimation) {
      const timerList: any[] = [];
      const intervalList: any[] = [];
      const actionData = AvatarActionConfig.getInstance().getAction(curAnimation);
      if (actionData) {
        const playSoundTime = actionData.playSoundTime;
        if (playSoundTime > 0) {
          timerList.push(
            setTimeout(() => {
              AudioSystem.playAudio(audioKey, actionData.soundUrl, () => {
                return true;
              });
              intervalList.push(
                setInterval(() => {
                  AudioSystem.playAudio(audioKey, actionData.soundUrl, () => {
                    return true;
                  });
                }, actionData.actionTime)
              );
            }, playSoundTime)
          );
        }
      }
      return () => {
        timerList.forEach((timer) => {
          clearTimeout(timer);
        });
        intervalList.forEach((interval) => {
          clearInterval(interval);
        });
      };
    }
  }, [curAnimation, audioKey]);
  return null;
}

export default function EntityAnimationTSX({
  animations,
  animation,
  face,
  root,
}: {
  root: THREE.Object3D;
  face: EntityFace | undefined;
  animation: EntityAnimation;
  animations: THREE.AnimationClip[];
  children?: React.ReactNode;
}) {
  const [curAnimation, setCurAnimation] = useState(animation.curAnimation);
  const [jumpAnimation, setJumpAnimation] = useState(animation.jumpAnimation);
  const audioKey = useMemo(() => {
    return THREE.MathUtils.generateUUID();
  }, []);
  useEffect(() => {
    const update = () => {
      setCurAnimation(animation.curAnimation);
      setJumpAnimation(animation.jumpAnimation);
    };
    update();
    animation.registerUpdateCallback(update);
    return () => {
      animation.registerUpdateCallback(() => undefined);
    };
  }, [animation]);
  useEffect(() => {
    const jumpClips: THREE.AnimationClip[] = [];
    for (let i = 0; i < animations.length; i++) {
      const jumpClip = animations[i];
      if (jumpClip.name === jumpAnimation) {
        jumpClip.name = 'jump';
        //切割跳跃动作
        const maxDuration = Math.floor(jumpClip.duration * 10) / 10;
        const ranges = [
          { start: 0, end: Math.min(1.5, maxDuration), name: 'jump_01' },
          { start: 0.2, end: Math.min(1.5, maxDuration), name: 'jump_02' },
          { start: 1.05, end: Math.min(1.5, maxDuration), name: 'jump_03' },
          { start: 0.8, end: 1.05, name: 'fall' },
        ];
        const clip = jumpClip.clone();
        const tracks = clip.tracks; // 动画的所有轨迹

        ranges.forEach((range) => {
          const { start, end } = range;
          const newTracks: THREE.VectorKeyframeTrack[] = [];
          tracks.map((track) => {
            const startIndex = track.times.findIndex((time) => time >= start);
            const endIndex = track.times.findIndex((time) => time > end);

            // 如果没有关键帧，跳过
            if (startIndex === -1 || endIndex === -1 || startIndex === endIndex) {
              // console.warn(`No valid keyframes found for track ${track.name} in range ${start} to ${end}`);
              return;
            }

            // 克隆轨迹并截取时间和值
            const newTrack = track.clone();
            newTrack.times = track.times.slice(startIndex, endIndex).map((time) => time - start);
            const valueSize = track.getValueSize(); // 每个时间点的值大小
            newTrack.values = track.values.slice(startIndex * valueSize, endIndex * valueSize);

            // 确保 `times` 和 `values` 不为空
            if (newTrack.times.length === 0 || newTrack.values.length === 0) {
              console.warn(`Empty track: ${track.name} after slicing. Skipping.`);
              return;
            }
            newTracks.push(newTrack);
          });

          const newClip = new THREE.AnimationClip(range.name, end - start, newTracks);
          jumpClips.push(newClip);
        });
      }
    }
    animations.push(...jumpClips);
  }, [animations, jumpAnimation]);
  return (
    <>
      <AudioSystemComponent _key={audioKey} />
      {face && <EntityFaceTSX curAnimation={curAnimation} face={face} root={root} />}
      <Animation
        audioKey={audioKey}
        root={root}
        animation={animation}
        curAnimation={curAnimation}
        animationsRef={animations}
      />
    </>
  );
}
