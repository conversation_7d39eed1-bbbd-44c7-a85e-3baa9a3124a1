import * as THREE from 'three';
import { useCallback, useEffect, useRef, useState } from 'react';
import { ComponentType } from '@/game/TS/Entity/Enum';
import { Entity } from '@/game/TS/Entity/Entity';
import { EntityTransform } from '@/game/TS/Entity/Components/EntityTransform';
import EntityTransformTSX from '@/game/TSX/Entity/Components/EntityTransform';
import { EntityGlbMesh } from '@/game/TS/Entity/Components/EntityGlbMesh';
import EntityGlbMeshTSX from '@/game/TSX/Entity/Components/EntityGlbMesh';
import { EntityAnimation } from '@/game/TS/Entity/Components/EntityAnimation';
import EntityAnimationTSX from '@/game/TSX/Entity/Components/EntityAnimation';
import { EntityAvatarMesh } from '@/game/TS/Entity/Components/EntityAvatarMesh';
import EntityAvatarMeshTSX from '@/game/TSX/Entity/Components/EntityAvatarMesh';
import { EntityFollowBone } from '@/game/TS/Entity/Components/EntityFollowBone';
import EntityFollowBoneTSX from '@/game/TSX/Entity/Components/EntityFollowBone';
import { EntityEffect } from '@/game/TS/Entity/Components/EntityEffect';
import EntityEffectTSX from '@/game/TSX/Entity/Components/EntityEffect';
import { EntityPizzaMesh } from '@/game/TS/Entity/Components/EntityPizzaMesh';
import EntityPizzaMeshTSX from '@/game/TSX/Entity/Components/EntityPizzaMesh';
import { EntityTopText } from '@/game/TS/Entity/Components/EntityTopText';
import EntityTopTextTSX from '@/game/TSX/Entity/Components/EntityTopText';
import { EntityFace } from '@/game/TS/Entity/Components/EntityFace';
import { EntityChatBubble } from '@/game/TS/Entity/Components/EntityChatBubble';
import EntityChatBubbleTSX from '@/game/TSX/Entity/Components/EntityChatBubble';
import { EntityWalkPoint } from '@/game/TS/Entity/Components/EntityWalkPoint';
import EntityWalkPointTSX from '@/game/TSX/Entity/Components/EntityWalkPoint';
import { EntityCollider } from '@/game/TS/Entity/Components/EntityCollider';
import EntityColliderTSX from '@/game/TSX/Entity/Components/EntityCollider';

function CreateMesh({
  entity,
  updateAnimations,
}: {
  entity: Entity;
  updateAnimations: (group: THREE.Object3D, animations: THREE.AnimationClip[]) => void;
}) {
  const glbMesh = entity.getComponent<EntityGlbMesh>(ComponentType.GlbMesh);
  const avatarMesh = entity.getComponent<EntityAvatarMesh>(ComponentType.AvatarMesh);
  const effect = entity.getComponent<EntityEffect>(ComponentType.Effect);
  const pizzaMesh = entity.getComponent<EntityPizzaMesh>(ComponentType.PizzaMesh);
  return (
    <>
      {glbMesh && (
        <EntityGlbMeshTSX glbMesh={glbMesh} updateAnimations={updateAnimations} entity={entity} />
      )}
      {avatarMesh && (
        <EntityAvatarMeshTSX
          avatarMesh={avatarMesh}
          updateAnimations={updateAnimations}
          entity={entity}
        />
      )}
      {effect && <EntityEffectTSX effect={effect} />}
      {pizzaMesh && <EntityPizzaMeshTSX pizzaMesh={pizzaMesh} />}
    </>
  );
}

function AnimationAndMesh({ entity }: { entity: Entity }) {
  const animation = entity.getComponent<EntityAnimation>(ComponentType.Animation);
  const face = entity.getComponent<EntityFace>(ComponentType.Face);
  const topText = entity.getComponent<EntityTopText>(ComponentType.TopText);
  const collider = entity.getComponent<EntityCollider>(ComponentType.Collider);
  const chatBubble = entity.getComponent<EntityChatBubble>(ComponentType.ChatBubble);
  const [animations, setAnimations] = useState<THREE.AnimationClip[]>([]);
  const [root, setRoot] = useState<THREE.Object3D | null>(null);

  const update = useCallback((group: THREE.Object3D, newAnimations: THREE.AnimationClip[]) => {
    setAnimations(newAnimations);
    setRoot(group);
  }, []);
  return (
    <>
      {animation && root && (
        <EntityAnimationTSX root={root} face={face} animation={animation} animations={animations} />
      )}
      {topText && root && <EntityTopTextTSX root={root} topText={topText} />}
      {collider && root && <EntityColliderTSX root={root} collider={collider} />}
      {chatBubble && root && <EntityChatBubbleTSX root={root} chatBubble={chatBubble} />}
      <CreateMesh entity={entity} updateAnimations={update} />
    </>
  );
}

export function EntityTSX({ entity }: { entity: Entity }) {
  const transform = entity.getComponent<EntityTransform>(ComponentType.Transform);
  const followBone = entity.getComponent<EntityFollowBone>(ComponentType.FollowBone);
  const walkPoint = entity.getComponent<EntityWalkPoint>(ComponentType.WalkPoint);
  const groupRef = useRef<THREE.Group>(null);
  useEffect(() => {
    entity.setEntityRoot(groupRef.current);
  }, [entity, groupRef]);
  return (
    <group ref={groupRef}>
      {transform && (
        <EntityTransformTSX transform={transform}>
          <AnimationAndMesh entity={entity} />
        </EntityTransformTSX>
      )}
      {!transform && followBone && (
        <EntityFollowBoneTSX followBone={followBone}>
          <AnimationAndMesh entity={entity} />
        </EntityFollowBoneTSX>
      )}
      {!transform && walkPoint && (
        <EntityWalkPointTSX walkPoint={walkPoint}>
          <AnimationAndMesh entity={entity} />
        </EntityWalkPointTSX>
      )}
    </group>
  );
}
