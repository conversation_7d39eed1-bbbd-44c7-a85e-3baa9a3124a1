import * as THREE from 'three';
import { EntityManager } from '@/game/TS/Entity/EntityManager';

export class NetPlayer {
  private btcAddress: string;
  private entityId: number;

  constructor(btcAddress: string, entityId: number) {
    this.btcAddress = btcAddress;
    this.entityId = entityId;
  }

  getAddress() {
    return this.btcAddress;
  }

  getPosition() {
    return (
      EntityManager.getInstance().getEntity(this.entityId)?.getPosition() ||
      new THREE.Vector3(0, 0, 0)
    );
  }

  setVisible(visible: boolean) {
    EntityManager.getInstance().getEntity(this.entityId);
  }
}
