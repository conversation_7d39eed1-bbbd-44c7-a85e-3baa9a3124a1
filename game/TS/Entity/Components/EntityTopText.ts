import { ComponentType } from '../Enum';
import { EntityComponent } from './EntityComponent';
import { game } from '@/game/Proto/generated/game_messages';

enum ActionType {
  UpdateText = 1,
  UpdateIcon = 2,
}

export class EntityTopText extends EntityComponent {
  topText = '';
  textColor = '#FFFFFF';
  topIconName = '';

  hideMyself = false;

  override init() {
    super.init();

    this.registerAction(ActionType.UpdateText, game.CommonMessage, (data: game.CommonMessage) => {
      this.topText = data.messageList[0];
      this.textColor = data.messageList[1];
      this.selfUpdate();
    });
    this.registerAction(ActionType.UpdateIcon, game.CommonMessage, (data: game.CommonMessage) => {
      this.topIconName = data.messageList[0];
      this.selfUpdate();
    });
  }

  override getType() {
    return ComponentType.TopText;
  }

  override syncToServer() {
    this.sendText();
    this.sendIconName();
  }

  private sendText() {
    this.sendAction(
      ActionType.UpdateText,
      game.CommonMessage.create({
        messageList: [this.topText, this.textColor],
      }).toJSON()
    );
  }

  private sendIconName() {
    this.sendAction(
      ActionType.UpdateIcon,
      game.CommonMessage.create({
        messageList: [this.topIconName],
      }).toJSON()
    );
  }

  setText(topText: string, textColor = '#FFFFFF') {
    this.topText = topText;
    this.textColor = textColor;
    this.selfUpdate();
    this.sendText();
  }

  setTopIconName(iconName: string) {
    this.topIconName = iconName;
    this.selfUpdate();
    this.sendIconName();
  }

  setHideMyself(hide: boolean) {
    this.hideMyself = hide;
    this.selfUpdate();
  }
}
